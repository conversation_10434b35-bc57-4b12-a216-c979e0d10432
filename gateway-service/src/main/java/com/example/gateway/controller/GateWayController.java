package com.example.gateway.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/gateway")
public class GateWayController {
    @Value("${spring.application.name}")
    private String name;

    @GetMapping("/test1")
    public String test1(){
        return name;
    }
}
