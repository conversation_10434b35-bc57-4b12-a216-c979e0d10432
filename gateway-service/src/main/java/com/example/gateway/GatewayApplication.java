package com.example.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 网关服务启动类
 * 纯网关服务，不依赖数据库、缓存等存储组件
 * 只负责路由转发和JWT Token验证
 */
@SpringBootApplication
@EnableDiscoveryClient
public class GatewayApplication {

    public static void main(String[] args) {
        //解决bootstrap.yml 文件找不到问题 boot版本高于2.4
        System.setProperty("spring.cloud.bootstrap.enabled", "true");
        SpringApplication.run(GatewayApplication.class, args);
    }
}
