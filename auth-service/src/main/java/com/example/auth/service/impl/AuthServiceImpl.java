package com.example.auth.service.impl;

import com.example.auth.dto.RegisterRequest;
import com.example.auth.service.AuthService;
import com.example.common.utils.JwtUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

/**
 * 认证服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final PasswordEncoder passwordEncoder;

    @Override
    public String login(String username, String password) {
        try {
            log.info("User login attempt: {}", username);
            
            // TODO: 从数据库验证用户名和密码
            // 这里简化处理，实际应该查询数据库
            if ("admin".equals(username) && "123456".equals(password)) {
                String token = JwtUtils.generateToken(username, 1L);
                log.info("User login successful: {}", username);
                return token;
            }
            
            throw new RuntimeException("用户名或密码错误");
            
        } catch (Exception e) {
            log.error("<PERSON><PERSON> failed for user: {}", username, e);
            throw new RuntimeException("登录失败: " + e.getMessage());
        }
    }

    @Override
    public void register(RegisterRequest request) {
        try {
            log.info("User registration attempt: {}", request.getUsername());
            
            // TODO: 检查用户名是否已存在
            // TODO: 保存用户到数据库
            String encodedPassword = passwordEncoder.encode(request.getPassword());
            
            log.info("User registration successful: {}", request.getUsername());
            
        } catch (Exception e) {
            log.error("Registration failed for user: {}", request.getUsername(), e);
            throw new RuntimeException("注册失败: " + e.getMessage());
        }
    }

    @Override
    public boolean validateToken(String token) {
        try {
            String username = JwtUtils.getUsernameFromToken(token);
            return JwtUtils.validateToken(token, username);
        } catch (Exception e) {
            log.error("Token validation failed", e);
            return false;
        }
    }

    @Override
    public String refreshToken(String token) {
        try {
            String username = JwtUtils.getUsernameFromToken(token);
            Long userId = JwtUtils.getUserIdFromToken(token);
            return JwtUtils.generateToken(username, userId);
        } catch (Exception e) {
            log.error("Token refresh failed", e);
            throw new RuntimeException("Token刷新失败: " + e.getMessage());
        }
    }
}
