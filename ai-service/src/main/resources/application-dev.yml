# 开发环境配置
spring:
  # 数据源配置 - 本地MySQL数据库
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************************************
    username: root
    password: root
    # HikariCP连接池配置
    hikari:
      maximum-pool-size: 10
      minimum-idle: 2
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # Redis配置 - 本地Redis
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

# MyBatis Plus配置 - 开发环境简化版
mybatis-plus:
  configuration:
    # 开发环境也移除SQL日志，减少控制台输出，需要时可临时启用
    # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置 - 开发环境简化版
logging:
  level:
    # 开发环境使用INFO级别，减少调试信息
    com.example.ai: INFO
    # 移除Spring Web的DEBUG日志，减少HTTP请求详细信息
    # org.springframework.web: DEBUG
