# ==========================================
# AI服务配置文件
# 集成Spring AI框架，提供智能对话、文本生成等AI功能
# ==========================================

# 服务器配置
server:
  port: 8083  # AI服务端口
  servlet:
    context-path: # 应用上下文路径
    encoding:
      charset: UTF-8  # 字符编码
      enabled: true
      force: true
  # Tomcat配置
  tomcat:
    max-threads: 200  # 最大工作线程数
    min-spare-threads: 10  # 最小空闲线程数

# Spring框架配置
spring:
  servlet:
    multipart:
      enabled: true  # 启用文件上传
      max-file-size: 10MB  # 单个文件最大大小
      max-request-size: 50MB  # 请求最大大小
      file-size-threshold: 2KB  # 文件写入磁盘的阈值
  # Spring Cloud配置
  cloud:
    # Nacos服务注册与发现配置
    nacos:
      discovery:
        server-addr: ************:8848  # Nacos服务器地址（线上环境）
        namespace: # 命名空间，用于环境隔离
        group: DEFAULT_GROUP  # 服务分组
        cluster-name: DEFAULT  # 集群名称
        metadata:  # 服务元数据
          version: 1.0.0
          description: "AI智能服务，提供对话、文本生成等功能"
          contact: "<EMAIL>"
    # Spring Cloud Circuit Breaker配置
    circuitbreaker:
      resilience4j:
        enabled: true  # 启用Resilience4j作为Circuit Breaker实现

  # 数据源配置 - MySQL数据库
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver  # MySQL 8.x驱动
    url: **********************************************************************************************************************************************
    username: root  # 数据库用户名
    password: Admin@123!  # 数据库密码
    # HikariCP连接池配置
    hikari:
      maximum-pool-size: 20  # 连接池最大连接数
      minimum-idle: 5  # 连接池最小空闲连接数
      connection-timeout: 30000  # 连接超时时间(毫秒)
      idle-timeout: 600000  # 空闲连接超时时间(毫秒)
      max-lifetime: 1800000  # 连接最大生存时间(毫秒)
      leak-detection-threshold: 60000  # 连接泄漏检测阈值(毫秒)

  # Redis配置 - 用于缓存和向量存储
  data:
    redis:
      host: ************  # Redis服务器地址
      port: 6379  # Redis端口
      database: 3  # 使用数据库3，避免与其他服务冲突
      password: 123456 # Redis密码，如果设置了密码请填写
      timeout: 10000ms  # 连接超时时间
      # Lettuce连接池配置
      lettuce:
        pool:
          max-active: 8  # 连接池最大连接数
          max-wait: -1ms  # 连接池最大阻塞等待时间
          max-idle: 8  # 连接池最大空闲连接数
          min-idle: 0  # 连接池最小空闲连接数
        shutdown-timeout: 100ms  # 关闭超时时间



# MyBatis Plus配置 - ORM框架配置
mybatis-plus:
  # MyBatis配置
  configuration:
    map-underscore-to-camel-case: true  # 下划线转驼峰命名
    cache-enabled: true  # 启用二级缓存
    # 移除SQL日志输出，减少控制台日志量，如需调试可临时启用
    # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # SQL日志输出到控制台
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler  # 枚举类型处理器
    lazy-loading-enabled: true  # 启用延迟加载
    aggressive-lazy-loading: false  # 禁用积极的延迟加载
  # 全局配置
  global-config:
    # 数据库配置
    db-config:
      id-type: ASSIGN_ID  # 主键生成策略：雪花算法
      logic-delete-field: deleted  # 逻辑删除字段名
      logic-delete-value: 0  # 逻辑删除值(已删除)
      logic-not-delete-value: 1  # 逻辑删除值(未删除)
      table-underline: true  # 表名下划线命名
  # Mapper XML文件位置
  mapper-locations: classpath*:mapper/*.xml
  # 实体类包路径
  type-aliases-package: com.example.ai.entity

# OpenFeign配置 - 微服务间调用（统一配置）
feign:
  client:
    config:
      default:  # 默认配置，应用于所有Feign客户端
        connect-timeout: 5000  # 连接超时时间(毫秒)
        read-timeout: 10000  # 读取超时时间(毫秒)
        logger-level: basic  # 日志级别：NONE, BASIC, HEADERS, FULL
      business-service:  # 针对business-service的特殊配置
        connect-timeout: 3000  # 业务服务连接超时
        read-timeout: 8000  # 业务服务读取超时
        logger-level: basic
      payment-service:  # 针对payment-service的特殊配置
        connect-timeout: 5000  # 支付服务连接超时
        read-timeout: 15000  # 支付服务读取超时（支付操作可能较慢）
        logger-level: basic
  # 启用压缩
  compression:
    request:
      enabled: true  # 启用请求压缩
      mime-types: text/xml,application/xml,application/json  # 压缩的MIME类型
      min-request-size: 2048  # 最小压缩请求大小
    response:
      enabled: true  # 启用响应压缩
  # 启用Circuit Breaker支持
  circuitbreaker:
    enabled: true  # 启用Circuit Breaker以支持fallback功能
    alphanumeric-ids:
      enabled: true  # 启用字母数字ID
  # 禁用Hystrix，使用Resilience4j替代
  hystrix:
    enabled: false


  # 重试配置
  retry:
    configs:
      default:
        max-attempts: 3  # 最大重试次数
        wait-duration: 1s  # 重试间隔
        retry-exceptions:
          - java.net.ConnectException
          - java.net.SocketTimeoutException
          - org.springframework.web.client.ResourceAccessException
    instances:
      business-service:
        base-config: default
      payment-service:
        base-config: default
        max-attempts: 2  # 支付服务重试次数较少，避免重复扣款

  # 限流配置
  ratelimiter:
    configs:
      default:
        limit-for-period: 100  # 时间窗口内允许的请求数
        limit-refresh-period: 1s  # 时间窗口
        timeout-duration: 0s  # 获取许可的超时时间
    instances:
      business-service:
        base-config: default
      payment-service:
        base-config: default
        limit-for-period: 50  # 支付服务限流更严格



# Resilience4j配置 - 熔断器配置
# 注意：Circuit Breaker的具体配置在CircuitBreakerConfig.java中进行
# 这里只保留必要的全局配置，优化为快速触发降级
resilience4j:
  circuitbreaker:
    configs:
      default:
        # 全局默认配置，具体实例配置在Java配置类中
        register-health-indicator: true  # 注册健康指示器
        event-consumer-buffer-size: 10  # 事件消费者缓冲区大小
        # 快速触发降级的配置
        failure-rate-threshold: 30  # 失败率阈值30%
        minimum-number-of-calls: 1  # 最小调用次数1次
        sliding-window-size: 3  # 滑动窗口大小3次
        wait-duration-in-open-state: 3s  # 熔断器打开状态等待时间3秒
  # 超时配置 - 与Circuit Breaker配合使用
  timelimiter:
    configs:
      default:
        timeout-duration: 500ms  # 默认超时时间500毫秒，快速失败
        cancel-running-future: true  # 取消运行中的Future


# 日志配置 - 优化降级功能调试
logging:
  level:
    # 根日志级别设置为INFO
    root: info
    # AI服务核心包设置为INFO级别，降级相关信息使用INFO级别输出
    com.example.ai: info  # 自定义包的日志级别
    # Feign相关日志设置为INFO级别，确保降级信息能正常输出
    com.example.ai.feign: info  # Feign客户端日志级别
    com.example.ai.monitor: info  # 监控组件日志级别
    com.example.ai.exception: info  # 异常处理日志级别
    com.example.ai.controller: info  # 控制器日志级别
    # OpenFeign框架日志 - 设置为INFO，确保能看到降级相关信息
    org.springframework.cloud.openfeign: info  # OpenFeign框架日志级别
    feign: info  # Feign核心日志级别，确保降级信息可见
    # Circuit Breaker相关日志 - 设置为INFO，确保状态变化能看到
    io.github.resilience4j: info  # Resilience4j日志级别
    org.springframework.cloud.circuitbreaker: info  # Circuit Breaker日志级别
    # 框架日志设置为WARN级别，只显示警告和错误
    org.springframework.ai: warn  # Spring AI框架日志级别
    org.mybatis: warn  # MyBatis日志级别
    com.baomidou.mybatisplus: warn  # MyBatis Plus日志级别
    # 数据库连接池日志设置为ERROR级别
    com.zaxxer.hikari: error  # HikariCP连接池日志
    # Spring框架核心日志设置为WARN级别
    org.springframework: warn
    # 禁用一些可能产生噪音的日志
    org.springframework.web.servlet.DispatcherServlet: error
    org.springframework.boot.autoconfigure: error
  pattern:
    # 简化的日志输出格式，突出重要信息
    console: "%d{HH:mm:ss.SSS} [%level] %logger{30} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%level] %logger{50} - %msg%n"
  file:
    name: logs/ai-service.log  # 日志文件路径
    max-size: 50MB  # 减小单个日志文件大小
    max-history: 15  # 减少保留的日志文件数量

# 文件上传配置
file:
  upload:
    # 文件上传路径 - 本地存储路径
    path: ${FILE_UPLOAD_PATH:./uploads/}
    # 访问URL前缀 - 用于生成HTTP访问地址
    url-prefix: ${FILE_UPLOAD_URL_PREFIX:http://localhost:8083/files/}
    # 允许的文件类型
    allowed-types: ${FILE_UPLOAD_ALLOWED_TYPES:jpg,jpeg,png,gif,bmp,webp}
    # 单个文件最大大小（MB）
    max-file-size: ${FILE_UPLOAD_MAX_SIZE:10}
    # 是否启用文件上传功能
    enabled: ${FILE_UPLOAD_ENABLED:true}


# 管理端点配置 - 用于健康检查和监控
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus  # 暴露的端点
  endpoint:
    health:
      show-details: always  # 显示健康检查详情
  metrics:
    export:
      prometheus:
        enabled: true  # 启用Prometheus指标导出
