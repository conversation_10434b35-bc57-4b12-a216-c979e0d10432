# AI服务配置
ai:
  # DashScope配置
  dashscope:
    # API密钥 - 建议通过环境变量设置
    api-key: ${DASHSCOPE_API_KEY:sk-fbbbf1f6917e4c0cb7ef2d30cd647547}
    # 基础URL
    base-url: ${DASHSCOPE_BASE_URL:https://dashscope.aliyuncs.com/compatible-mode/v1}
    # 默认模型
    model: ${DASHSCOPE_MODEL:qwen-plus}

  # 流式对话配置
  stream:
    # 超时时间（毫秒）- 默认30分钟
    timeout: ${AI_STREAM_TIMEOUT:1800000}

  # 线程池管理配置
  thread-pool:
    # 是否启用线程池管理
    enabled: ${AI_THREAD_POOL_ENABLED:true}

    # 监控配置
    monitor:
      # 是否启用监控
      enabled: ${AI_THREAD_POOL_MONITOR_ENABLED:true}
      # 监控间隔时间（秒）
      interval-seconds: ${AI_THREAD_POOL_MONITOR_INTERVAL:30}
      # 是否记录详细日志
      detailed-logging: ${AI_THREAD_POOL_MONITOR_DETAILED:false}

    # AI流式处理线程池配置
    stream:
      # 核心线程数
      core-size: ${AI_STREAM_THREAD_POOL_CORE:5}
      # 最大线程数
      max-size: ${AI_STREAM_THREAD_POOL_MAX:20}
      # 线程空闲时间（秒）
      keep-alive: ${AI_STREAM_THREAD_POOL_KEEP_ALIVE:60}
      # 队列容量
      queue-capacity: ${AI_STREAM_THREAD_POOL_QUEUE:100}
      # 线程名称前缀
      thread-name-prefix: ${AI_STREAM_THREAD_POOL_NAME_PREFIX:ai-stream-chat}

# 日志配置
logging:
  level:
    com.example.ai.service.impl.AiServiceImpl: INFO
    com.example.ai.threadpool: INFO
    # 开发环境可以设置为DEBUG查看详细日志
    # com.example.ai.service.impl.AiServiceImpl: DEBUG
    # com.example.ai.threadpool: DEBUG
