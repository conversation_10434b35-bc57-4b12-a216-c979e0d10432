package com.example.ai.controller;



import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.example.ai.feign.BusinessFeign;
import com.example.ai.util.JsonUtils;
import com.example.ai.dto.ChatInfoResponse;
import com.example.ai.dto.ChatRequest;
import com.example.ai.dto.ChatResponse;
import com.example.ai.entity.ChatInfo;
import com.example.ai.service.AiService;
import com.example.ai.service.ChatInfoService;
import com.example.ai.service.ChatService;
import com.example.common.entity.vo.AiChatMessageVo;
import com.example.common.result.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * AI控制器
 */
@RestController
@RequestMapping("/ai")
@RequiredArgsConstructor
//@CrossOrigin
public class AiController {

    private final AiService aiService;

    private final ChatService chatService;

    private final ChatInfoService chatInfoService;

    private final BusinessFeign businessFeign;



    /**
     * 单轮对话文本输出(OpenAI)
     *
     * @param message
     * @return
     */
    @GetMapping("/singleChatCompletionText")
    public Result singleChatCompletionTest(String message, String chatId) {
        return Result.success(aiService.singleChatCompletionText(message, chatId));
    }

    /**
     * 单轮对话json输出 (DashScope)
     *
     * @param message
     * @return
     */
    @GetMapping("/singleGenerationResultJson")
    public Result singleGenerationResultJson(String message) {
        return Result.success(aiService.singleGenerationResultJson(message));
    }

    /**
     * 多轮对话 (DashScope)
     *
     * @param message
     * @return
     */
    @GetMapping("/multiwheelGenerationResultJson")
    public Result multiwheelGenerationResultJson(String message, Integer type, String chatId) {
        return Result.success(aiService.multiwheelGenerationResultJson(message, type, chatId));
    }

    /**
     * 流式对话输出 (DashScope)
     * 使用Server-Sent Events (SSE) 实现实时流式输出
     *
     * @param message 用户消息
     * @param chatId 对话ID
     * @return SseEmitter 流式响应
     */
    @GetMapping(value = "/stream", produces = "text/event-stream;charset=UTF-8")
    public SseEmitter streamChat(@RequestParam String message,
                                @RequestParam(required = false) String chatId) {
        return aiService.streamChat(message, chatId);
    }

    /***
     * 创建新对话
     * @param chatRequest
     * @return
     */
    @PostMapping("/createConversation")
    public Result createConversation(@RequestBody @Validated ChatRequest chatRequest) {

        return Result.success(chatService.createConversation(chatRequest));
    }

    /**
     * 获取对话列表
     *
     * @return
     */
    @GetMapping("/conversations")
    public Result getConversations() {
        return Result.success(chatService.getConversations());
    }


    /**
     * 更新对话标题
     *
     * @param conversationId
     * @return
     */
    @PutMapping("/updateConversation/{conversationId}")
    public Result updateConversation(@PathVariable String conversationId, @RequestBody ChatRequest chatRequest) {
        chatService.updateConversationTitle(conversationId, chatRequest.getTitle());
        return Result.success();
    }

    /**
     * 根据会话id获取会话详细列表信息
     *
     * @param chatId
     * @return
     */
    @GetMapping("/chatInfo/{chatId}/messages")
    public Result chatInfo(@PathVariable String chatId) {
        List<AiChatMessageVo> chatInfoResponseList = chatInfoService.getByChatId(chatId);
        return Result.success(chatInfoResponseList);
    }

    /**
     * 清空对话
     * @param conversationId
     * @return
     */
    @DeleteMapping("/clearConversation/{conversationId}/clear")
    public Result clearConversation(@PathVariable String conversationId) {
        LambdaUpdateWrapper<ChatInfo> updateWrapper = new LambdaUpdateWrapper<ChatInfo>()
                .eq(ChatInfo::getChatId, conversationId)
                .set(ChatInfo::getDelStatus, 0);
        chatInfoService.update(updateWrapper);
        return Result.success();
    }


}
