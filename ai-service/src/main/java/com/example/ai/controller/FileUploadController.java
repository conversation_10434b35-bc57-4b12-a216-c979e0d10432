package com.example.ai.controller;

import com.example.ai.dto.FileUploadDto;
import com.example.ai.service.FileUploadService;
import com.example.ai.vo.FileUploadVo;
import com.example.common.result.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传控制器
 * 提供文件上传相关的REST API接口
 *
 * <AUTHOR> Service
 * @date 2025-07-24
 */
@Slf4j
@RestController
@RequestMapping("/api/file")
@RequiredArgsConstructor
//@CrossOrigin
public class FileUploadController {

    private final FileUploadService fileUploadService;

    /**
     * 上传单个文件
     *
     * @param file 上传的文件
     * @param userId 用户ID（可选）
     * @param category 文件分类（可选）
     * @param description 文件描述（可选）
     * @return 文件上传结果
     */
    @PostMapping("/upload")
    public Result<FileUploadVo> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "category", required = false) String category,
            @RequestParam(value = "description", required = false) String description) {

        log.info("收到文件上传请求，文件名: {}, 大小: {} bytes, 用户ID: {}, 分类: {}",
                file.getOriginalFilename(), file.getSize(), userId, category);

        // 构建上传参数
        FileUploadDto uploadDto = FileUploadDto.builder()
                .userId(userId)
                .category(category)
                .description(description)
                .generateThumbnail(false)
                .build();

        // 执行文件上传
        FileUploadVo result = fileUploadService.uploadFile(file, uploadDto);

        log.info("文件上传成功，文件ID: {}, 访问URL: {}", result.getFileId(), result.getFileUrl());

        return Result.success("文件上传成功", result);
    }

    /**
     * 上传图片文件（专门用于图片上传）
     *
     * @param file 上传的图片文件
     * @param userId 用户ID（可选）
     * @param description 图片描述（可选）
     * @return 图片上传结果
     */
    @PostMapping("/upload/image")
    public Result<FileUploadVo> uploadImage(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "description", required = false) String description) {

        log.info("收到图片上传请求，文件名: {}, 大小: {} bytes, 用户ID: {}",
                file.getOriginalFilename(), file.getSize(), userId);

        // 构建上传参数，指定分类为image
        FileUploadDto uploadDto = FileUploadDto.builder()
                .userId(userId)
                .category("image")
                .description(description)
                .generateThumbnail(true)  // 图片可以生成缩略图
                .build();

        // 执行文件上传
        FileUploadVo result = fileUploadService.uploadFile(file, uploadDto);

        log.info("图片上传成功，文件ID: {}, 访问URL: {}", result.getFileId(), result.getFileUrl());

        return Result.success("图片上传成功", result);
    }

    /**
     * 删除文件
     *
     * @param fileId 文件ID
     * @return 删除结果
     */
    @DeleteMapping("/{fileId}")
    public Result<Boolean> deleteFile(@PathVariable String fileId) {
        log.info("收到文件删除请求，文件ID: {}", fileId);

        boolean success = fileUploadService.deleteFile(fileId);

        if (success) {
            log.info("文件删除成功，文件ID: {}", fileId);
            return Result.success("文件删除成功", true);
        } else {
            log.warn("文件删除失败，文件ID: {}", fileId);
            return Result.error("文件删除失败");
        }
    }

    /**
     * 获取文件信息
     *
     * @param fileId 文件ID
     * @return 文件信息
     */
    @GetMapping("/{fileId}")
    public Result<FileUploadVo> getFileInfo(@PathVariable String fileId) {
        log.info("收到获取文件信息请求，文件ID: {}", fileId);

        FileUploadVo fileInfo = fileUploadService.getFileInfo(fileId);

        if (fileInfo != null) {
            return Result.success("获取文件信息成功", fileInfo);
        } else {
            return Result.error("文件不存在");
        }
    }

    /**
     * 健康检查接口
     *
     * @return 服务状态
     */
    @GetMapping("/health")
    public Result<String> health() {
        return Result.success("文件上传服务运行正常");
    }
}
