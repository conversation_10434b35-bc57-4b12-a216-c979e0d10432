package com.example.ai.controller;

import com.example.ai.feign.PayMentFeign;
import com.example.ai.service.PayMentService;
import com.example.common.entity.vo.VipPlanVo;
import com.example.common.result.Result;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * Create by 2025/7/23 17:09
 * desc 支付服务vip相关
 */
@RequiredArgsConstructor
@RequestMapping("/vip")
@RestController
//@CrossOrigin
public class PayMentVipController {

    private final PayMentService payMentService;

    /**
     *  获取所有vip套餐以及套餐特性
     * @return
     */
    @GetMapping("/plans")
    public Result getAllVipPlansFeatures() {
        return Result.success(payMentService.getVipPlans()) ;
    }

    /**
     * 获取支付方式列表
     * @return
     */
    @GetMapping("/paymentMethodList")
    public Result getPaymentMethodList() {
        return Result.success(payMentService.paymentMethodList()) ;
    }


    /**
     * 获取客户端充值相关信息
     * @return
     */
    @GetMapping("/getRechargeList")
    public Result getRechargeList(){
        return Result.success(payMentService.getRechargeList());
    }


    /**
     * 获取测试支付宝扫码支付预付款支付页面
     * @return
     */
    @GetMapping("/aliPayCode")
    public Result aliPayCode(){
        return Result.success(payMentService.getAliPayCode());
    }



    /**
     * 获取订单号
     * @return
     */
    @GetMapping("/getOrderNum")
    public Result getOrderNum(){
        return Result.success(payMentService.getOrderNum());
    }


    /**
     * 根据支付类型 获取预支付下单页面 以及唯一订单号
     * @param payType
     * @return
     */
    @PostMapping("/getPagePayCode")
    public Result getPagePayCode(String payType){
        ConcurrentHashMap<String, Object> concurrentHashMap = payMentService.getPagePayCode(payType);
        return Result.success(concurrentHashMap);
    }
}
