package com.example.ai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.ai.entity.AIChatSession;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * AI聊天会话Mapper接口
 * 提供聊天会话的数据库操作方法
 */
@Mapper
public interface ChatSessionMapper extends BaseMapper<AIChatSession> {

    /**
     * 根据用户ID查询活跃的会话列表
     * @param userId 用户ID
     * @return 会话列表
     */
    @Select("SELECT * FROM ai_chat_session WHERE user_id = #{userId} AND status = 1 AND deleted = 0 " +
            "ORDER BY last_message_time DESC")
    List<AIChatSession> selectActiveByUserId(@Param("userId") Long userId);

    /**
     * 根据会话ID查询会话信息
     * @param sessionId 会话ID
     * @return 会话信息
     */
    @Select("SELECT * FROM ai_chat_session WHERE session_id = #{sessionId} AND deleted = 0")
    AIChatSession selectBySessionId(@Param("sessionId") String sessionId);

    /**
     * 更新会话的消息统计信息
     * @param sessionId 会话ID
     * @param messageCount 消息数量
     * @param totalTokens 总token数
     * @return 更新行数
     */
    @Update("UPDATE ai_chat_session SET message_count = #{messageCount}, total_tokens = #{totalTokens}, " +
            "last_message_time = NOW() WHERE session_id = #{sessionId}")
    int updateMessageStats(@Param("sessionId") String sessionId,
                          @Param("messageCount") Integer messageCount,
                          @Param("totalTokens") Integer totalTokens);

    /**
     * 结束会话
     * @param sessionId 会话ID
     * @return 更新行数
     */
    @Update("UPDATE ai_chat_session SET status = 2 WHERE session_id = #{sessionId}")
    int endSession(@Param("sessionId") String sessionId);

    /**
     * 统计用户的会话总数
     * @param userId 用户ID
     * @return 会话总数
     */
    @Select("SELECT COUNT(*) FROM ai_chat_session WHERE user_id = #{userId} AND deleted = 0")
    Integer countByUserId(@Param("userId") Long userId);

    /**
     * 查询用户最近的会话
     * @param userId 用户ID
     * @param limit 查询数量限制
     * @return 会话列表
     */
    @Select("SELECT * FROM ai_chat_session WHERE user_id = #{userId} AND deleted = 0 " +
            "ORDER BY last_message_time DESC LIMIT #{limit}")
    List<AIChatSession> selectRecentByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);
}
