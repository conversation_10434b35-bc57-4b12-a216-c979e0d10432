package com.example.ai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.ai.dto.ChatInfoResponse;
import com.example.ai.dto.ChatResponse;
import com.example.ai.entity.Chat;
import com.example.ai.entity.ChatInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface ChatInfoMapper extends BaseMapper<ChatInfo> {

    /**
     * 根据会话id获取会话详细列表信息
     * @param chatId
     * @return
     */
    @Select("SELECT *,chat_info_content as content FROM chat_info WHERE chat_id = #{chatId} AND del_status = 1")
    List<ChatInfoResponse> getByChatId(String chatId);

    @Select("SELECT *,chat_info_content as content FROM chat_info WHERE chat_id = #{chatId} AND del_status = 1 AND type = 'ai'")
    List<ChatInfoResponse> getByChatIdAndType(String chatId);

}
