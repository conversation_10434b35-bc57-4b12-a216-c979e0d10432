package com.example.ai.util;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 简化的日志切面类
 * 用于记录AI服务中Controller和Service方法的执行情况
 * 只记录核心信息：方法名、执行时间、执行状态
 */
@Slf4j
@Aspect
@Component
public class LogAspect {

    @PostConstruct
    public void init() {
        log.info("LogAspect 切面类已初始化");
    }

    // 匹配所有controller包下的方法
    @Pointcut("execution(public * com.example.ai.controller.*.*(..))")
    private void controllerLog() {
    }

    // 匹配所有AI服务相关的方法
    @Pointcut("execution(public * com.example.ai.service.*.*(..))")
    private void serviceLog() {
    }

    /**
     * Controller方法的简化日志记录
     */
    @Around("controllerLog()")
    public Object logController(ProceedingJoinPoint pjp) throws Throwable {
        String methodName = pjp.getSignature().getDeclaringTypeName() + "." + pjp.getSignature().getName();
        long startTime = System.currentTimeMillis();

        try {
            Object result = pjp.proceed();
            long duration = System.currentTimeMillis() - startTime;
            log.info("Controller: {} 执行成功, 耗时: {}ms", methodName, duration);
            return result;
        } catch (Throwable e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("Controller: {} 执行失败, 耗时: {}ms, 错误: {}", methodName, duration, e.getMessage());
            throw e;
        }
    }

    /**
     * Service方法的简化日志记录
     */
    @Around("serviceLog()")
    public Object logService(ProceedingJoinPoint pjp) throws Throwable {
        String methodName = pjp.getSignature().getDeclaringTypeName() + "." + pjp.getSignature().getName();
        long startTime = System.currentTimeMillis();

        try {
            Object result = pjp.proceed();
            long duration = System.currentTimeMillis() - startTime;

            // AI服务调用超过5秒时发出警告
            if (duration > 5000) {
                log.warn("Service: {} 执行耗时过长: {}ms", methodName, duration);
            } else {
                log.info("Service: {} 执行成功, 耗时: {}ms", methodName, duration);
            }

            return result;
        } catch (Throwable e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("Service: {} 执行失败, 耗时: {}ms, 错误: {}", methodName, duration, e.getMessage());
            throw e;
        }
    }
}
