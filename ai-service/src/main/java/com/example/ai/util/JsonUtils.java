package com.example.ai.util;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * JSON工具类
 * 基于FastJSON2实现Java对象与JSON之间的转换
 * 
 * <AUTHOR> Service
 * @since 1.0.0
 */
@Slf4j
public class JsonUtils {

    /**
     * 将Java对象转换为JSON字符串
     * 
     * @param obj 要转换的对象
     * @return JSON字符串，转换失败返回null
     */
    public static String toJsonString(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return JSON.toJSONString(obj);
        } catch (Exception e) {
            log.error("对象转JSON字符串失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将Java对象转换为格式化的JSON字符串（美化输出）
     * 
     * @param obj 要转换的对象
     * @return 格式化的JSON字符串，转换失败返回null
     */
    public static String toJsonStringPretty(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return JSON.toJSONString(obj, JSONWriter.Feature.PrettyFormat);
        } catch (Exception e) {
            log.error("对象转格式化JSON字符串失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将Java对象转换为JSONObject
     * 
     * @param obj 要转换的对象
     * @return JSONObject对象，转换失败返回null
     */
    public static JSONObject toJsonObject(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return (JSONObject) JSON.toJSON(obj);
        } catch (Exception e) {
            log.error("对象转JSONObject失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为指定类型的对象
     * 
     * @param jsonString JSON字符串
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 转换后的对象，转换失败返回null
     */
    public static <T> T parseObject(String jsonString, Class<T> clazz) {
        if (jsonString == null || jsonString.trim().isEmpty() || clazz == null) {
            return null;
        }
        try {
            return JSON.parseObject(jsonString, clazz);
        } catch (Exception e) {
            log.error("JSON字符串转对象失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为List集合
     * 
     * @param jsonString JSON字符串
     * @param clazz 集合元素类型
     * @param <T> 泛型类型
     * @return List集合，转换失败返回null
     */
    public static <T> List<T> parseArray(String jsonString, Class<T> clazz) {
        if (jsonString == null || jsonString.trim().isEmpty() || clazz == null) {
            return null;
        }
        try {
            return JSON.parseArray(jsonString, clazz);
        } catch (Exception e) {
            log.error("JSON字符串转List失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为JSONObject
     * 
     * @param jsonString JSON字符串
     * @return JSONObject对象，转换失败返回null
     */
    public static JSONObject parseObject(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }
        try {
            return JSON.parseObject(jsonString);
        } catch (Exception e) {
            log.error("JSON字符串转JSONObject失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为Map
     * 
     * @param jsonString JSON字符串
     * @return Map对象，转换失败返回null
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> parseToMap(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }
        try {
            return JSON.parseObject(jsonString, Map.class);
        } catch (Exception e) {
            log.error("JSON字符串转Map失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 检查字符串是否为有效的JSON格式
     * 
     * @param jsonString 要检查的字符串
     * @return true表示有效的JSON，false表示无效
     */
    public static boolean isValidJson(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return false;
        }
        try {
            JSON.parseObject(jsonString);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 深度克隆对象（通过JSON序列化和反序列化）
     * 
     * @param obj 要克隆的对象
     * @param clazz 对象类型
     * @param <T> 泛型类型
     * @return 克隆后的对象，克隆失败返回null
     */
    public static <T> T deepClone(T obj, Class<T> clazz) {
        if (obj == null || clazz == null) {
            return null;
        }
        try {
            String jsonString = toJsonString(obj);
            return parseObject(jsonString, clazz);
        } catch (Exception e) {
            log.error("对象深度克隆失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将对象转换为Map
     * 
     * @param obj 要转换的对象
     * @return Map对象，转换失败返回null
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> objectToMap(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            String jsonString = toJsonString(obj);
            return parseToMap(jsonString);
        } catch (Exception e) {
            log.error("对象转Map失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将Map转换为指定类型的对象
     *
     * @param map Map对象
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 转换后的对象，转换失败返回null
     */
    public static <T> T mapToObject(Map<String, Object> map, Class<T> clazz) {
        if (map == null || clazz == null) {
            return null;
        }
        try {
            String jsonString = toJsonString(map);
            return parseObject(jsonString, clazz);
        } catch (Exception e) {
            log.error("Map转对象失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 解析通义千问API响应JSON，获取message内容
     *
     * @param jsonString 通义千问API响应的JSON字符串
     * @return message内容，解析失败返回null
     */
    public static String parseQwenMessage(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            log.warn("JSON字符串为空");
            return null;
        }

        try {
            // 解析JSON字符串为JSONObject
            JSONObject jsonObject = JSON.parseObject(jsonString);

            // 获取output对象
            JSONObject output = jsonObject.getJSONObject("output");
            if (output == null) {
                log.warn("JSON中未找到output字段");
                return null;
            }

            // 获取choices数组
            JSONArray choices = output.getJSONArray("choices");
            if (choices == null || choices.isEmpty()) {
                log.warn("JSON中未找到choices数组或数组为空");
                return null;
            }

            // 获取第一个choice的message
            JSONObject firstChoice = choices.getJSONObject(0);
            if (firstChoice == null) {
                log.warn("choices数组第一个元素为空");
                return null;
            }

            JSONObject message = firstChoice.getJSONObject("message");
            if (message == null) {
                log.warn("第一个choice中未找到message字段");
                return null;
            }

            // 获取message的content内容
            String content = message.getString("content");
            if (content == null) {
                log.warn("message中未找到content字段");
                return null;
            }

            log.info("成功解析通义千问响应，获取到message内容");
            return content;

        } catch (Exception e) {
            log.error("解析通义千问JSON响应失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 解析通义千问API响应JSON，获取完整的message对象
     *
     * @param jsonString 通义千问API响应的JSON字符串
     * @return message对象的Map形式，解析失败返回null
     */
    public static Map<String, Object> parseQwenMessageObject(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            log.warn("JSON字符串为空");
            return null;
        }

        try {
            // 解析JSON字符串为JSONObject
            JSONObject jsonObject = JSON.parseObject(jsonString);

            // 获取output对象
            JSONObject output = jsonObject.getJSONObject("output");
            if (output == null) {
                log.warn("JSON中未找到output字段");
                return null;
            }

            // 获取choices数组
            JSONArray choices = output.getJSONArray("choices");
            if (choices == null || choices.isEmpty()) {
                log.warn("JSON中未找到choices数组或数组为空");
                return null;
            }

            // 获取第一个choice的message
            JSONObject firstChoice = choices.getJSONObject(0);
            if (firstChoice == null) {
                log.warn("choices数组第一个元素为空");
                return null;
            }

            JSONObject message = firstChoice.getJSONObject("message");
            if (message == null) {
                log.warn("第一个choice中未找到message字段");
                return null;
            }

            // 将JSONObject转换为Map
            Map<String, Object> messageMap = new HashMap<>();
            for (String key : message.keySet()) {
                messageMap.put(key, message.get(key));
            }

            log.info("成功解析通义千问响应，获取到完整message对象");
            return messageMap;

        } catch (Exception e) {
            log.error("解析通义千问JSON响应失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 解析通义千问API响应JSON，获取所有choices中的message列表
     *
     * @param jsonString 通义千问API响应的JSON字符串
     * @return message列表，解析失败返回null
     */
    public static List<Map<String, Object>> parseQwenAllMessages(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            log.warn("JSON字符串为空");
            return null;
        }

        try {
            // 解析JSON字符串为JSONObject
            JSONObject jsonObject = JSON.parseObject(jsonString);

            // 获取output对象
            JSONObject output = jsonObject.getJSONObject("output");
            if (output == null) {
                log.warn("JSON中未找到output字段");
                return null;
            }

            // 获取choices数组
            JSONArray choices = output.getJSONArray("choices");
            if (choices == null || choices.isEmpty()) {
                log.warn("JSON中未找到choices数组或数组为空");
                return null;
            }

            // 遍历所有choices，获取message
            List<Map<String, Object>> messageList = new java.util.ArrayList<>();
            for (int i = 0; i < choices.size(); i++) {
                JSONObject choice = choices.getJSONObject(i);
                if (choice != null) {
                    JSONObject message = choice.getJSONObject("message");
                    if (message != null) {
                        Map<String, Object> messageMap = new HashMap<>();
                        for (String key : message.keySet()) {
                            messageMap.put(key, message.get(key));
                        }
                        messageList.add(messageMap);
                    }
                }
            }

            log.info("成功解析通义千问响应，获取到{}个message对象", messageList.size());
            return messageList;

        } catch (Exception e) {
            log.error("解析通义千问JSON响应失败: {}", e.getMessage(), e);
            return null;
        }
    }
}
