package com.example.ai;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * AI服务启动类
 * 集成MySQL和Redis，提供AI聊天记录持久化和缓存功能
 */
@SpringBootApplication(scanBasePackages = {"com.example.ai", "com.example.common"})
@MapperScan("com.example.ai.mapper")
@EnableDiscoveryClient
@EnableFeignClients(basePackages = "com.example.ai.feign")

public class AiApplication {

    public static void main(String[] args) {
        try {
            //解决bootstrap.yml 文件找不到问题 boot版本高于2.4
            System.setProperty("spring.cloud.bootstrap.enabled", "true");
            SpringApplication.run(AiApplication.class, args);
        } catch (Exception e) {
            System.err.println("应用启动失败: " + e.getMessage());
            if (e.getMessage().contains("Access denied")) {
                System.err.println("数据库连接被拒绝，请检查：");
                System.err.println("1. 数据库服务器地址是否正确");
                System.err.println("2. 用户名密码是否正确");
                System.err.println("3. 数据库服务器是否允许远程连接");
                System.err.println("4. 防火墙设置是否正确");
            }
            e.printStackTrace();
        }
    }
}
