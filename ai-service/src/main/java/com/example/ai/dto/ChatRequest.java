package com.example.ai.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 聊天请求DTO
 */
@Data
public class ChatRequest {

    @NotBlank(message = "消息内容不能为空")
    private String title;

    @NotNull(message = "用户ID不能为空")
    private Long userId = 1L;

    private String model = "qwen-plus"; // 默认使用OpenAI模型

    private String sessionId; // 会话ID，用于上下文管理

    private Double temperature = 0.7; // 温度参数

    private Integer maxTokens = 1000; // 最大token数
}
