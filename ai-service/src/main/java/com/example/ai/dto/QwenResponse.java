package com.example.ai.dto;

import lombok.Data;
import java.util.List;

/**
 * 通义千问API响应DTO
 * 用于映射通义千问API的响应结构
 * 
 * <AUTHOR> Service
 * @since 1.0.0
 */
@Data
public class QwenResponse {
    
    /**
     * 输出结果
     */
    private Output output;
    
    /**
     * 请求ID
     */
    private String requestId;
    
    /**
     * 使用情况统计
     */
    private Usage usage;
    
    /**
     * 输出结果内部类
     */
    @Data
    public static class Output {
        /**
         * 选择列表
         */
        private List<Choice> choices;
    }
    
    /**
     * 选择内部类
     */
    @Data
    public static class Choice {
        /**
         * 完成原因
         */
        private String finishReason;
        
        /**
         * 消息内容
         */
        private Message message;
    }
    
    /**
     * 消息内部类
     */
    @Data
    public static class Message {
        /**
         * 消息内容
         */
        private String content;
        
        /**
         * 角色
         */
        private String role;
    }
    
    /**
     * 使用情况统计内部类
     */
    @Data
    public static class Usage {
        /**
         * 输入token数
         */
        private Integer inputTokens;
        
        /**
         * 输出token数
         */
        private Integer outputTokens;
        
        /**
         * 总token数
         */
        private Integer totalTokens;
    }
}
