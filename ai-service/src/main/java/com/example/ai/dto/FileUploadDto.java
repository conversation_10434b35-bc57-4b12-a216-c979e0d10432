package com.example.ai.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 文件上传请求DTO
 * 用于接收文件上传请求的参数
 * 
 * <AUTHOR> Service
 * @date 2025-07-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class FileUploadDto {

    /**
     * 用户ID（可选）
     */
    private Long userId;

    /**
     * 文件分类（可选）
     * 例如：avatar（头像）、document（文档）、image（图片）等
     */
    private String category;

    /**
     * 文件描述（可选）
     */
    private String description;

    /**
     * 是否生成缩略图（可选，默认false）
     */
    private Boolean generateThumbnail = false;
}
