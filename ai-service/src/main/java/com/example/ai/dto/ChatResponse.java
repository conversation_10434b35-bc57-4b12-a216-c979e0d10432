package com.example.ai.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 聊天响应DTO
 */
@Data
@Accessors(chain = true)
public class ChatResponse {
    private String id;

    private String title;

    private Long userId;

    private String model;

    private String sessionId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;

    private Integer tokenUsage; // token使用量

    private String messageId; // 消息ID

    private Date createdAt;

    private Date updatedAt;
}
