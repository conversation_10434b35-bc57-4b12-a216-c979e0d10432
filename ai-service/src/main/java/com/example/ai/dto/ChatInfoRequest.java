package com.example.ai.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 聊天响应DTO
 */
@Data
@Accessors(chain = true)
public class ChatInfoRequest {
    private String chatId;

    private String chatInfoTitle;

    private String aiResponse;

    private String chatInfoContent;

    private String type ;


}
