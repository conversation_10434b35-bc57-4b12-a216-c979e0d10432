package com.example.ai.service.impl;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.ai.dto.ChatRequest;
import com.example.ai.dto.ChatResponse;
import com.example.ai.entity.Chat;
import com.example.common.exception.UserFriendlyException;
import com.example.ai.feign.BusinessFeign;
import com.example.ai.mapper.ChatMapper;
import com.example.ai.service.ChatService;
import com.example.common.entity.dto.AiChatSessionDto;
import com.example.common.result.Result;
import com.example.common.entity.vo.AiChatSessionVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class ChatServiceImpl extends ServiceImpl<ChatMapper, Chat> implements ChatService {
    @Resource
    private ChatMapper chatMapper;
    @Resource
    private BusinessFeign businessFeign;

    @Override
    public AiChatSessionVo createConversation(ChatRequest chatRequest) {
        try {
            log.info("开始通过Feign创建会话，title: {}, userId: {}, model: {}",
                    chatRequest.getTitle(), chatRequest.getUserId(), chatRequest.getModel());

            // 构建请求DTO
            AiChatSessionDto aiChatSessionDto = AiChatSessionDto.builder()
                    .title(chatRequest.getTitle())
                    .userId(chatRequest.getUserId())
                    .model(chatRequest.getModel())
                    .build();

            // 调用远程服务
            Result<AiChatSessionVo> conversation = businessFeign.createConversation(aiChatSessionDto);

            if (conversation != null && conversation.getCode() == 200 && conversation.getData() != null) {
                log.info("通过Feign创建会话成功，sessionId: {}", conversation.getData().getId());
                return conversation.getData();
            } else {
                // 处理不同的错误情况，提供更具体的用户友好信息
                String userMessage;
                String developerMessage;

                if (conversation == null) {
                    userMessage = "服务连接异常，请稍后重试";
                    developerMessage = "Feign调用返回null";
                } else if (conversation.getCode() == 503) {
                    userMessage = conversation.getMessage(); // 使用降级服务返回的用户友好信息
                    developerMessage = "服务降级: " + conversation.getMessage();
                } else {
                    userMessage = "创建会话失败，请稍后重试";
                    developerMessage = "创建会话失败，错误码: " + conversation.getCode() + ", 错误信息: " + conversation.getMessage();
                }

                log.error("通过Feign创建会话失败: {}", developerMessage);
                throw new UserFriendlyException(userMessage, developerMessage);
            }
        } catch (UserFriendlyException e) {
            throw e; // 直接抛出用户友好异常
        } catch (feign.FeignException e) {
            // 专门处理Feign异常
            String userMessage = "服务暂时不可用，请稍后重试";
            String developerMessage = "Feign调用异常: " + e.getMessage();
            log.error("Feign调用异常，title: {}, userId: {}, error: {}",
                    chatRequest.getTitle(), chatRequest.getUserId(), e.getMessage(), e);
            throw new UserFriendlyException(userMessage, developerMessage);
        } catch (Exception e) {
            // 处理其他异常
            String userMessage = "系统异常，请稍后重试";
            String developerMessage = "创建会话异常: " + e.getMessage();
            log.error("创建会话异常，title: {}, userId: {}, error: {}",
                    chatRequest.getTitle(), chatRequest.getUserId(), e.getMessage(), e);
            throw new UserFriendlyException(userMessage, developerMessage);
        }
    }

    @Override
    public List<AiChatSessionVo> getConversations() {
        try {
            log.info("开始获取会话列表");

            List<AiChatSessionVo> conversations = businessFeign.getConversations();

            // 检查是否是降级响应（空列表可能表示服务不可用）
            if (conversations != null && conversations.isEmpty()) {
                log.warn("获取到空的会话列表，可能是服务降级响应");
            }

            log.info("成功获取会话列表，数量: {}", conversations != null ? conversations.size() : 0);
            return conversations;
        } catch (feign.FeignException e) {
            // 专门处理Feign异常
            String userMessage = "获取会话列表服务暂时不可用，请稍后重试";
            String developerMessage = "Feign调用异常: " + e.getMessage();
            log.error("获取会话列表Feign调用失败: {}", e.getMessage(), e);
            throw new UserFriendlyException(userMessage, developerMessage);
        } catch (Exception e) {
            // 处理其他异常
            String userMessage = "获取会话列表失败，请稍后重试";
            String developerMessage = "获取会话列表异常: " + e.getMessage();
            log.error("获取会话列表失败: {}", e.getMessage(), e);
            throw new UserFriendlyException(userMessage, developerMessage);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateConversationTitle(String conversationId, String title) {
        // 参数验证
        if (!StringUtils.hasText(conversationId)) {
            throw new IllegalArgumentException("会话ID不能为空");
        }
        if (!StringUtils.hasText(title)) {
            throw new IllegalArgumentException("会话标题不能为空");
        }

        try {
            log.info("调用远程服务开始更新会话标题，conversationId: {}, title: {}", conversationId, title);

            AiChatSessionDto aiChatSessionDto = AiChatSessionDto.builder().sessionId(conversationId).title(title).build();
            // 调用远程服务
            Result result = businessFeign.updateConversation(aiChatSessionDto);

            if (result.getCode() == 200) {
                log.info("会话标题更新成功，conversationId: {}, newTitle: {}", conversationId, title);
            } else {
                log.error("会话标题更新失败，可能会话不存在，conversationId: {}", conversationId);
                throw new RuntimeException("会话标题更新失败，会话可能不存在");
            }
        } catch (Exception e) {
            log.error("更新会话标题异常，conversationId: {}, title: {}, error: {}",
                    conversationId, title, e.getMessage(), e);
            throw new RuntimeException("更新会话标题失败: " + e.getMessage(), e);
        }
    }
}
