package com.example.ai.service;

import com.example.ai.dto.ChatRequest;
import com.example.ai.dto.ChatResponse;
import io.reactivex.Flowable;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;

/**
 * AI服务接口
 */
public interface AiService {

    /**
     * 单轮对话文本输出(OpenAI)
     * @param message
     * @param chatId
     * @return
     */
    String singleChatCompletionText(String message,String chatId);


    /**
     * 单轮对话json输出 (DashScope)
     * @param message
     * @return
     */
    String singleGenerationResultJson(String message);


    /**
     * 多轮对话(DashScope)
     * @param message
     * @return
     */
    String multiwheelGenerationResultJson(String message,Integer type,String chatId);

    /**
     * 流式对话输出 (DashScope)
     * @param message 用户消息
     * @param chatId 对话ID
     * @return SseEmitter 用于流式输出
     */
    SseEmitter streamChat(String message, String chatId);


}
