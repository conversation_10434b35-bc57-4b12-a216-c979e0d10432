package com.example.ai.service.impl;

import com.example.ai.feign.PayMentFeign;
import com.example.ai.service.PayMentService;
import com.example.common.entity.vo.PaymentMethodVo;
import com.example.common.entity.vo.VipPlanVo;
import com.example.common.exception.RemoteServiceException;
import com.example.common.result.Result;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * Create by 2025/7/23 17:13
 * desc
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PayMentServiceImpl implements PayMentService {

    private final PayMentFeign payMentFeign;


    @Override
    public List<VipPlanVo> getVipPlans() {
        try {
            Result<List<VipPlanVo>> result = payMentFeign.getAllVipPlansFeatures();

            // 检查结果是否成功
            if (result != null && result.getCode() == 200) {
                log.info("获取VIP套餐列表成功");
                return result.getData();
            } else {
                // 服务端返回业务错误
                String errorMsg = result != null ? result.getMessage() : "获取VIP套餐列表失败";
                log.error("获取VIP套餐列表业务失败: {}", errorMsg);
                throw new RemoteServiceException("payment-service", result != null ? result.getCode() : 500, errorMsg);
            }
        } catch (FeignException e) {
            // Feign调用异常，提取服务端异常信息
            String errorMessage = extractFeignErrorMessage(e);
            log.error("获取VIP套餐列表Feign调用失败: {}", errorMessage, e);
            throw new RemoteServiceException("payment-service", e.status(), errorMessage, e);
        } catch (RemoteServiceException e) {
            // 重新抛出自定义异常
            throw e;
        } catch (Exception e) {
            // 其他未知异常
            log.error("获取VIP套餐列表未知异常: {}", e.getMessage(), e);
            throw new RemoteServiceException("payment-service", "获取VIP套餐列表时发生未知错误: " + e.getMessage(), e);
        }
    }

    @Override
    public List<PaymentMethodVo> paymentMethodList() {
        try {
            Result<List<PaymentMethodVo>> result = payMentFeign.paymentMethodList();

            // 检查结果是否成功
            if (result != null && result.getCode() == 200) {
                log.info("获取支付方式列表成功");
                return result.getData();
            } else {
                // 服务端返回业务错误
                String errorMsg = result != null ? result.getMessage() : "获取支付方式列表失败";
                log.error("获取支付方式列表业务失败: {}", errorMsg);
                throw new RemoteServiceException("payment-service", result != null ? result.getCode() : 500, errorMsg);
            }
        } catch (FeignException e) {
            // Feign调用异常，提取服务端异常信息
            String errorMessage = extractFeignErrorMessage(e);
            log.error("获取支付方式列表Feign调用失败: {}", errorMessage, e);
            throw new RemoteServiceException("payment-service", e.status(), errorMessage, e);
        } catch (RemoteServiceException e) {
            // 重新抛出自定义异常
            throw e;
        } catch (Exception e) {
            // 其他未知异常
            log.error("获取支付方式列表未知异常: {}", e.getMessage(), e);
            throw new RemoteServiceException("payment-service", "获取支付方式列表时发生未知错误: " + e.getMessage(), e);
        }
    }

    @Override
    public Result getRechargeList() {
        try {
            Result result = payMentFeign.getRechargeList();

            // 检查结果是否成功
            if (result != null && result.getCode() == 200) {
                log.info("获取充值列表成功");
                return Result.success(result.getData());
            } else {
                // 服务端返回业务错误
                String errorMsg = result != null ? result.getMessage() : "获取充值列表失败";
                log.error("获取充值列表业务失败: {}", errorMsg);
                return Result.error(result != null ? result.getCode() : 500, errorMsg);
            }
        } catch (FeignException e) {
            // Feign调用异常，提取服务端异常信息
            String errorMessage = extractFeignErrorMessage(e);
            log.error("获取充值列表Feign调用失败: {}", errorMessage, e);
            return Result.error(e.status(), "充值服务调用失败: " + errorMessage);
        } catch (Exception e) {
            // 其他未知异常
            log.error("获取充值列表未知异常: {}", e.getMessage(), e);
            return Result.error(500, "获取充值列表时发生未知错误: " + e.getMessage());
        }
    }

    @Override
    public Result getAliPayCode() {
        try {
            Result result = payMentFeign.getAliPayCode();

            // 检查结果是否成功
            if (result != null && result.getCode() == 200) {
                log.info("获取支付宝支付码成功");
                return Result.success(result.getData());
            } else {
                // 服务端返回业务错误
                String errorMsg = result != null ? result.getMessage() : "获取支付宝支付码失败";
                log.error("获取支付宝支付码业务失败: {}", errorMsg);
                return Result.error(result != null ? result.getCode() : 500, errorMsg);
            }
        } catch (FeignException e) {
            // Feign调用异常，提取服务端异常信息
            String errorMessage = extractFeignErrorMessage(e);
            log.error("获取支付宝支付码Feign调用失败: {}", errorMessage, e);
            return Result.error(e.status(), "支付宝支付服务调用失败: " + errorMessage);
        } catch (Exception e) {
            // 其他未知异常
            log.error("获取支付宝支付码未知异常: {}", e.getMessage(), e);
            return Result.error(500, "获取支付宝支付码时发生未知错误: " + e.getMessage());
        }
    }

    @Override
    public Result getOrderNum() {
        try {
            Result result = payMentFeign.getOrderNum();

            // 检查结果是否成功
            if (result != null && result.getCode() == 200) {
                log.info("获取订单号成功");
                return Result.success(result.getData());
            } else {
                // 服务端返回业务错误
                String errorMsg = result != null ? result.getMessage() : "获取订单号失败";
                log.error("获取订单号业务失败: {}", errorMsg);
                return Result.error(result != null ? result.getCode() : 500, errorMsg);
            }
        } catch (FeignException e) {
            // Feign调用异常，提取服务端异常信息
            String errorMessage = extractFeignErrorMessage(e);
            log.error("获取订单号Feign调用失败: {}", errorMessage, e);
            return Result.error(e.status(), "订单服务调用失败: " + errorMessage);
        } catch (Exception e) {
            // 其他未知异常
            log.error("获取订单号未知异常: {}", e.getMessage(), e);
            return Result.error(500, "获取订单号时发生未知错误: " + e.getMessage());
        }
    }

    @Override
    public ConcurrentHashMap<String, Object> getPagePayCode(String payType) {
        try {
            Result result = payMentFeign.getPagePayCode(payType);

            // 检查结果是否成功
            if (result != null && result.getCode() == 200) {
                log.info("获取支付页面成功");
                Object data = result.getData();
                return (ConcurrentHashMap<String, Object>) result.getData();
            } else {
                // 服务端返回业务错误
                String errorMsg = result != null ? result.getMessage() : "获取支付页面失败";
                log.error("获取支付页面业务失败: {}", errorMsg);
//                return Result.error(result != null ? result.getCode() : 500, errorMsg);
            }
        } catch (FeignException e) {
            // Feign调用异常，提取服务端异常信息
            String errorMessage = extractFeignErrorMessage(e);
            log.error("获取支付页面Feign调用失败: {}", errorMessage, e);
//            return Result.error(e.status(), "支付页面服务调用失败: " + errorMessage);
        } catch (Exception e) {
            // 其他未知异常
            log.error("获取支付页面未知异常: {}", e.getMessage(), e);
//            return Result.error(500, "获取支付页面时发生未知错误: " + e.getMessage());
        }
        return new ConcurrentHashMap<>();
    }

    /**
     * 从FeignException中提取错误信息
     *
     * @param e FeignException异常
     * @return 错误信息
     */
    private String extractFeignErrorMessage(FeignException e) {
        try {
            // 尝试获取响应体内容
            String responseBody = e.contentUTF8();
            if (responseBody != null && !responseBody.trim().isEmpty()) {
                // 如果响应体是纯文本错误信息，直接返回
                if (!responseBody.startsWith("{") && !responseBody.startsWith("[")) {
                    return responseBody.trim();
                }
                // 如果是JSON格式，可以进一步解析（这里简化处理）
                return responseBody;
            }
        } catch (Exception ex) {
            log.warn("解析Feign异常响应体失败: {}", ex.getMessage());
        }

        // 如果无法获取响应体，返回状态码和消息
        return String.format("HTTP %d: %s", e.status(), e.getMessage());
    }
}
