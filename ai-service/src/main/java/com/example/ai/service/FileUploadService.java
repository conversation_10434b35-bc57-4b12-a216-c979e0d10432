package com.example.ai.service;

import com.example.ai.dto.FileUploadDto;
import com.example.ai.vo.FileUploadVo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传服务接口
 * 提供文件上传、删除等功能
 * 
 * <AUTHOR> Service
 * @date 2025-07-24
 */
public interface FileUploadService {

    /**
     * 上传单个文件
     * 
     * @param file 上传的文件
     * @param uploadDto 上传参数
     * @return 文件上传结果
     */
    FileUploadVo uploadFile(MultipartFile file, FileUploadDto uploadDto);

    /**
     * 删除文件
     * 
     * @param fileId 文件ID
     * @return 是否删除成功
     */
    boolean deleteFile(String fileId);

    /**
     * 获取文件信息
     * 
     * @param fileId 文件ID
     * @return 文件信息
     */
    FileUploadVo getFileInfo(String fileId);
}
