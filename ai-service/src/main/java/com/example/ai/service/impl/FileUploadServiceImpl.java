package com.example.ai.service.impl;

import com.example.ai.config.FileUploadConfig;
import com.example.ai.dto.FileUploadDto;
import com.example.ai.service.FileUploadService;
import com.example.ai.vo.FileUploadVo;
import com.example.common.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import javax.annotation.PostConstruct;

/**
 * 文件上传服务实现类
 * 
 * <AUTHOR> Service
 * @date 2025-07-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileUploadServiceImpl implements FileUploadService {

    private final FileUploadConfig fileUploadConfig;

    /**
     * 服务初始化时创建基础上传目录
     */
    @PostConstruct
    public void init() {
        try {
            String basePath = fileUploadConfig.getPath();

            // 处理相对路径
            if (basePath.startsWith("./")) {
                basePath = System.getProperty("user.dir") + "/" + basePath.substring(2);
            } else if (!basePath.startsWith("/") && !basePath.contains(":")) {
                basePath = System.getProperty("user.dir") + "/" + basePath;
            }

            Path path = Paths.get(basePath);
            if (!Files.exists(path)) {
                Files.createDirectories(path);
                log.info("初始化创建上传根目录: {}", path.toAbsolutePath());
            } else {
                log.info("上传根目录已存在: {}", path.toAbsolutePath());
            }
        } catch (Exception e) {
            log.error("初始化上传目录失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public FileUploadVo uploadFile(MultipartFile file, FileUploadDto uploadDto) {
        // 检查文件上传功能是否启用
        if (!fileUploadConfig.getEnabled()) {
            throw new BusinessException("文件上传功能已禁用");
        }

        // 验证文件
        validateFile(file);

        try {
            // 生成文件信息
            String originalFileName = file.getOriginalFilename();
            String fileExtension = getFileExtension(originalFileName);
            String fileId = generateFileId();
            String storedFileName = generateStoredFileName(fileId, fileExtension);
            
            // 创建上传目录
            String uploadPath = createUploadDirectory(uploadDto.getCategory());
            
            // 保存文件
            Path filePath = Paths.get(uploadPath, storedFileName);
            file.transferTo(filePath.toFile());
            
            // 生成访问URL
            String fileUrl = generateFileUrl(uploadDto.getCategory(), storedFileName);
            
            // 构建返回结果
            return FileUploadVo.builder()
                    .fileId(fileId)
                    .originalFileName(originalFileName)
                    .storedFileName(storedFileName)
                    .fileExtension(fileExtension)
                    .fileSize(file.getSize())
                    .fileSizeFormatted(formatFileSize(file.getSize()))
                    .fileUrl(fileUrl)
                    .contentType(file.getContentType())
                    .category(uploadDto.getCategory())
                    .description(uploadDto.getDescription())
                    .uploadTime(LocalDateTime.now())
                    .userId(uploadDto.getUserId())
                    .build();
                    
        } catch (IOException e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
            throw new BusinessException("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public boolean deleteFile(String fileId) {
        // TODO: 实现文件删除逻辑
        // 这里需要根据fileId找到对应的文件路径并删除
        log.info("删除文件: {}", fileId);
        return true;
    }

    @Override
    public FileUploadVo getFileInfo(String fileId) {
        // TODO: 实现获取文件信息逻辑
        // 这里需要根据fileId查询文件信息
        log.info("获取文件信息: {}", fileId);
        return null;
    }

    /**
     * 验证上传的文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("上传文件不能为空");
        }

        // 检查文件大小
        if (file.getSize() > fileUploadConfig.getMaxFileSizeBytes()) {
            throw new BusinessException("文件大小超过限制，最大允许 " + fileUploadConfig.getMaxFileSize() + "MB");
        }

        // 检查文件类型
        String originalFileName = file.getOriginalFilename();
        if (!StringUtils.hasText(originalFileName)) {
            throw new BusinessException("文件名不能为空");
        }

        String fileExtension = getFileExtension(originalFileName);
        if (!fileUploadConfig.isAllowedType(fileExtension)) {
            throw new BusinessException("不支持的文件类型: " + fileExtension + 
                    "，支持的类型: " + fileUploadConfig.getAllowedTypes());
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (!StringUtils.hasText(fileName)) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        return fileName.substring(lastDotIndex + 1).toLowerCase();
    }

    /**
     * 生成文件ID
     */
    private String generateFileId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成存储文件名
     */
    private String generateStoredFileName(String fileId, String fileExtension) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        return timestamp + "_" + fileId + "." + fileExtension;
    }

    /**
     * 创建上传目录
     */
    private String createUploadDirectory(String category) throws IOException {
        String basePath = fileUploadConfig.getPath();

        // 处理相对路径，确保使用项目根目录
        if (basePath.startsWith("./")) {
            basePath = System.getProperty("user.dir") + "/" + basePath.substring(2);
        } else if (!basePath.startsWith("/") && !basePath.contains(":")) {
            // 相对路径转换为绝对路径
            basePath = System.getProperty("user.dir") + "/" + basePath;
        }

        String datePath = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));

        String uploadPath;
        if (StringUtils.hasText(category)) {
            uploadPath = basePath + category + "/" + datePath;
        } else {
            uploadPath = basePath + datePath;
        }

        // 标准化路径分隔符
        uploadPath = uploadPath.replace("\\", "/");

        Path path = Paths.get(uploadPath);
        if (!Files.exists(path)) {
            Files.createDirectories(path);
            log.info("创建上传目录: {}", path.toAbsolutePath());
        }

        return uploadPath;
    }

    /**
     * 生成文件访问URL
     */
    private String generateFileUrl(String category, String storedFileName) {
        String datePath = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String urlPrefix = fileUploadConfig.getUrlPrefix();
        
        if (StringUtils.hasText(category)) {
            return urlPrefix + category + "/" + datePath + "/" + storedFileName;
        } else {
            return urlPrefix + datePath + "/" + storedFileName;
        }
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }
}
