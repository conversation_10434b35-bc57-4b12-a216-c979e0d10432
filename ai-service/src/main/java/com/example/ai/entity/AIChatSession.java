package com.example.ai.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.example.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * AI聊天会话实体类
 * 用于管理用户的聊天会话
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_chat_session")
public class AIChatSession extends BaseEntity {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 会话ID，唯一标识
     */
    private String sessionId;

    /**
     * 会话标题
     */
    private String title;

    /**
     * 会话描述
     */
    private String description;

    /**
     * 使用的AI模型
     */
    private String model;

    /**
     * 会话状态：1-活跃，2-已结束，3-已删除
     */
    private Integer status;

    /**
     * 消息总数
     */
    private Integer messageCount;

    /**
     * 总token消耗
     */
    private Integer totalTokens;

    /**
     * 最后一条消息时间
     */
    private java.time.LocalDateTime lastMessageTime;

    /**
     * 会话配置（JSON格式）
     * 包含温度、最大token等参数
     */
    private String config;
}
