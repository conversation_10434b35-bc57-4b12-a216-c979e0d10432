package com.example.ai.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
//链式编程
@Accessors(chain = true)
@TableName("chat_info")
public class ChatInfo {

//    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    private String chatId;
    private String chatInfoTitle;
    private String aiResponse;
    private String chatInfoContent;
    private Long userId;
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    private Integer delStatus;
    private String type;
}
