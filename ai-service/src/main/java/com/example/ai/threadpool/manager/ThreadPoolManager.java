package com.example.ai.threadpool.manager;

import com.example.ai.threadpool.config.ThreadPoolProperties;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 极简线程池管理器
 * <p>
 * 该类负责统一管理AI服务中的所有线程池，提供以下核心功能：
 * <ul>
 *   <li>AI流式处理线程池的创建和管理</li>
 *   <li>线程池监控和健康状态检查</li>
 *   <li>优雅的线程池关闭机制</li>
 *   <li>线程池状态信息的统一获取</li>
 * </ul>
 *
 * <p>设计特点：
 * <ul>
 *   <li>简化设计：专注于AI服务的核心线程池需求</li>
 *   <li>监控集成：内置基本的线程池监控功能</li>
 *   <li>配置驱动：通过ThreadPoolProperties进行配置管理</li>
 *   <li>异常安全：完善的异常处理和资源清理机制</li>
 * </ul>
 *
 * <p>使用示例：
 * <pre>{@code
 * @Autowired
 * private ThreadPoolManager threadPoolManager;
 *
 * // 获取AI流式处理线程池
 * ExecutorService executor = threadPoolManager.getAiStreamThreadPool();
 *
 * // 提交异步任务
 * executor.submit(() -> {
 *     // AI流式处理逻辑
 * });
 *
 * // 检查健康状态
 * String health = threadPoolManager.getHealthStatus();
 * }</pre>
 *
 * <AUTHOR> Assistant
 * @since 1.0.0
 * @see ThreadPoolProperties 线程池配置属性
 * @see ThreadPoolExecutor Java标准线程池实现
 */
@Slf4j
@Component
public class ThreadPoolManager {

    /**
     * 线程池配置属性
     * <p>
     * 通过Spring的依赖注入机制自动装配，包含所有线程池的配置参数，
     * 如核心线程数、最大线程数、队列容量、监控配置等。
     *
     * @see ThreadPoolProperties 配置属性详细定义
     */
    @Autowired
    private ThreadPoolProperties threadPoolProperties;

    /**
     * AI流式处理线程池
     * <p>
     * 专门用于处理AI流式响应的线程池，具有以下特性：
     * <ul>
     *   <li>支持高并发的AI流式数据处理</li>
     *   <li>配置了合适的核心线程数和最大线程数</li>
     *   <li>使用有界队列防止内存溢出</li>
     *   <li>自定义线程工厂，便于线程识别和调试</li>
     *   <li>自定义拒绝策略，提供友好的错误提示</li>
     * </ul>
     *
     * <p>线程命名规则：ai-stream-{序号}
     *
     * @see #initAiStreamThreadPool() 初始化方法
     * @see #getAiStreamThreadPool() 获取方法
     */
    private ThreadPoolExecutor aiStreamThreadPool;

    /**
     * 监控定时任务线程池
     * <p>
     * 用于执行线程池监控任务的定时调度器，具有以下特性：
     * <ul>
     *   <li>单线程设计，避免监控任务之间的竞争</li>
     *   <li>守护线程模式，不会阻止JVM正常退出</li>
     *   <li>定时执行线程池状态检查和日志记录</li>
     *   <li>支持可配置的监控间隔</li>
     * </ul>
     *
     * <p>线程命名：thread-pool-monitor
     *
     * @see #initMonitorScheduler() 初始化方法
     * @see #startMonitoring() 启动监控方法
     */
    private ScheduledExecutorService monitorScheduler;
    
    /**
     * 初始化线程池
     * <p>
     * 在Spring容器完成依赖注入后自动调用，负责初始化所有线程池组件。
     * 该方法执行以下初始化步骤：
     * <ol>
     *   <li>检查线程池管理器是否启用</li>
     *   <li>初始化AI流式处理线程池</li>
     *   <li>初始化监控定时任务线程池</li>
     *   <li>启动线程池监控功能</li>
     * </ol>
     *
     * <p>如果初始化过程中发生任何异常，会记录错误日志并抛出运行时异常，
     * 确保应用启动失败而不是在运行时出现不可预期的问题。
     *
     * <p>配置控制：
     * 可以通过 {@code threadPoolProperties.enabled} 配置项来控制是否启用线程池管理器。
     * 当设置为false时，所有线程池都不会被创建，相关功能将被禁用。
     *
     * @throws RuntimeException 当线程池初始化失败时抛出
     * @see PostConstruct Spring生命周期注解
     * @see ThreadPoolProperties#getEnabled() 启用状态配置
     */
    @PostConstruct
    public void initThreadPools() {
        if (!threadPoolProperties.getEnabled()) {
            log.info("线程池管理器已禁用");
            return;
        }

        log.info("开始初始化线程池管理器...");

        try {
            // 初始化AI流式处理线程池
            initAiStreamThreadPool();

            // 初始化监控定时任务线程池
            initMonitorScheduler();

            // 启动监控
            startMonitoring();

            log.info("线程池管理器初始化完成");

        } catch (Exception e) {
            log.error("线程池管理器初始化失败", e);
            throw new RuntimeException("线程池管理器初始化失败", e);
        }
    }
    
    /**
     * 初始化AI流式处理线程池
     * <p>
     * 创建专门用于AI流式数据处理的线程池，该线程池具有以下特性和配置：
     *
     * <p><strong>线程工厂配置：</strong>
     * <ul>
     *   <li>自定义线程命名：使用配置的前缀 + 递增序号</li>
     *   <li>守护线程模式：设置为守护线程，不阻止JVM退出</li>
     *   <li>异常处理器：捕获并记录线程中的未处理异常</li>
     *   <li>线程安全计数：使用AtomicLong确保线程编号的原子性</li>
     * </ul>
     *
     * <p><strong>拒绝策略配置：</strong>
     * <ul>
     *   <li>记录详细的拒绝信息：包括当前活跃线程数和队列大小</li>
     *   <li>抛出明确的异常：提供用户友好的错误信息</li>
     *   <li>便于问题诊断：通过日志记录帮助定位性能瓶颈</li>
     * </ul>
     *
     * <p><strong>线程池参数：</strong>
     * <ul>
     *   <li>核心线程数：从配置中获取，保证基本的并发处理能力</li>
     *   <li>最大线程数：从配置中获取，应对突发的高并发场景</li>
     *   <li>线程存活时间：配置的keepAlive时间，优化资源使用</li>
     *   <li>工作队列：有界的LinkedBlockingQueue，防止内存溢出</li>
     * </ul>
     *
     * @see ThreadPoolProperties.StreamConfig 流式处理配置
     * @see ThreadFactory 线程工厂接口
     * @see RejectedExecutionHandler 拒绝策略接口
     * @see LinkedBlockingQueue 有界阻塞队列
     */
    private void initAiStreamThreadPool() {
        ThreadPoolProperties.StreamConfig streamConfig = threadPoolProperties.getStream();

        // 创建线程工厂 - 负责创建具有统一命名和配置的线程
        ThreadFactory threadFactory = new ThreadFactory() {
            // 线程编号计数器，确保线程名称的唯一性
            private final AtomicLong threadNumber = new AtomicLong(1);

            @Override
            public Thread newThread(Runnable r) {
                // 创建线程并设置名称
                Thread thread = new Thread(r, streamConfig.getThreadNamePrefix() + "-" + threadNumber.getAndIncrement());
                // 设置为守护线程，不阻止JVM正常退出
                thread.setDaemon(true);
                // 设置未捕获异常处理器，记录线程中的异常
                thread.setUncaughtExceptionHandler((t, e) ->
                    log.error("线程 {} 发生未捕获异常", t.getName(), e));
                return thread;
            }
        };

        // 创建拒绝策略 - 当线程池无法接受新任务时的处理逻辑
        RejectedExecutionHandler rejectedHandler = (r, executor) -> {
            // 记录拒绝执行的详细信息，便于问题诊断
            log.warn("AI流式处理线程池队列已满，拒绝执行任务。当前活跃线程数: {}, 队列大小: {}",
                    executor.getActiveCount(), executor.getQueue().size());
            // 抛出异常，提供明确的错误信息
            throw new RejectedExecutionException("AI流式处理线程池队列已满，请稍后重试");
        };

        // 创建线程池 - 使用ThreadPoolExecutor进行精确控制
        this.aiStreamThreadPool = new ThreadPoolExecutor(
                streamConfig.getCoreSize(),           // 核心线程数
                streamConfig.getMaxSize(),            // 最大线程数
                streamConfig.getKeepAlive(),          // 线程存活时间
                TimeUnit.SECONDS,                     // 时间单位
                new LinkedBlockingQueue<>(streamConfig.getQueueCapacity()), // 有界工作队列
                threadFactory,                        // 自定义线程工厂
                rejectedHandler                       // 自定义拒绝策略
        );

        log.info("AI流式处理线程池初始化完成 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}",
                streamConfig.getCoreSize(), streamConfig.getMaxSize(), streamConfig.getQueueCapacity());
    }
    
    /**
     * 初始化监控定时任务线程池
     * <p>
     * 创建用于执行线程池监控任务的定时调度器。该调度器具有以下特性：
     *
     * <p><strong>设计特点：</strong>
     * <ul>
     *   <li>单线程设计：避免监控任务之间的并发竞争</li>
     *   <li>守护线程：不会阻止JVM的正常退出</li>
     *   <li>固定命名：便于在线程转储中识别监控线程</li>
     *   <li>轻量级实现：专注于监控功能，不占用过多资源</li>
     * </ul>
     *
     * <p><strong>使用场景：</strong>
     * <ul>
     *   <li>定时收集线程池状态信息</li>
     *   <li>记录线程池性能指标</li>
     *   <li>检测线程池健康状态</li>
     *   <li>触发告警和通知</li>
     * </ul>
     *
     * @see ScheduledExecutorService 定时任务执行器
     * @see #startMonitoring() 启动监控方法
     */
    private void initMonitorScheduler() {
        // 创建单线程的定时调度器，使用Lambda表达式定义线程工厂
        this.monitorScheduler = Executors.newScheduledThreadPool(1, r -> {
            Thread thread = new Thread(r, "thread-pool-monitor");
            // 设置为守护线程，确保不阻止JVM退出
            thread.setDaemon(true);
            return thread;
        });

        log.info("监控定时任务线程池初始化完成");
    }

    /**
     * 启动监控
     * <p>
     * 根据配置启动线程池监控功能。监控系统会定期收集和记录线程池的状态信息，
     * 帮助开发人员了解系统的运行状况和性能表现。
     *
     * <p><strong>监控功能：</strong>
     * <ul>
     *   <li>定时记录线程池状态日志</li>
     *   <li>支持详细和简化两种日志模式</li>
     *   <li>可配置的监控间隔时间</li>
     *   <li>可通过配置完全禁用监控</li>
     * </ul>
     *
     * <p><strong>配置控制：</strong>
     * <ul>
     *   <li>{@code monitor.enabled}: 控制是否启用监控</li>
     *   <li>{@code monitor.intervalSeconds}: 设置监控间隔（秒）</li>
     *   <li>{@code monitor.detailedLogging}: 控制日志详细程度</li>
     * </ul>
     *
     * <p><strong>监控策略：</strong>
     * 使用固定频率调度，确保监控任务按照配置的间隔定期执行，
     * 不受单次监控任务执行时间的影响。
     *
     * @see ThreadPoolProperties.MonitorConfig 监控配置
     * @see #logThreadPoolStatus(boolean) 状态日志记录方法
     */
    private void startMonitoring() {
        if (!threadPoolProperties.getMonitor().getEnabled()) {
            log.info("线程池监控已禁用");
            return;
        }

        int intervalSeconds = threadPoolProperties.getMonitor().getIntervalSeconds();
        boolean detailedLogging = threadPoolProperties.getMonitor().getDetailedLogging();

        // 使用固定频率调度，定期执行监控任务
        monitorScheduler.scheduleAtFixedRate(
                () -> logThreadPoolStatus(detailedLogging),  // 监控任务
                intervalSeconds,                             // 初始延迟
                intervalSeconds,                             // 执行间隔
                TimeUnit.SECONDS                             // 时间单位
        );

        log.info("线程池监控已启动，监控间隔: {}秒", intervalSeconds);
    }
    
    /**
     * 获取AI流式处理线程池
     * <p>
     * 提供对AI流式处理线程池的访问接口。调用方可以使用返回的ExecutorService
     * 来提交AI相关的异步任务。
     *
     * <p><strong>使用注意事项：</strong>
     * <ul>
     *   <li>不要直接关闭返回的线程池，由ThreadPoolManager统一管理生命周期</li>
     *   <li>提交的任务应该是AI流式处理相关的，避免混用不同类型的任务</li>
     *   <li>在高并发场景下，注意监控线程池的状态，避免任务积压</li>
     *   <li>任务执行时间不宜过长，避免影响其他任务的执行</li>
     * </ul>
     *
     * <p><strong>返回值说明：</strong>
     * 返回的ExecutorService实际上是ThreadPoolExecutor的实例，
     * 支持所有标准的任务提交方法，如submit()、execute()等。
     *
     * @return AI流式处理线程池，如果未初始化则可能返回null
     * @see ExecutorService 标准执行器服务接口
     * @see ThreadPoolExecutor 具体的线程池实现
     */
    public ExecutorService getAiStreamThreadPool() {
        return aiStreamThreadPool;
    }

    /**
     * 获取线程池状态
     * <p>
     * 收集并返回所有线程池的详细状态信息，用于监控、调试和性能分析。
     * 返回的信息包括线程池的配置参数、运行时状态和性能指标。
     *
     * <p><strong>返回的状态信息包括：</strong>
     * <ul>
     *   <li><strong>corePoolSize</strong>: 核心线程数</li>
     *   <li><strong>maximumPoolSize</strong>: 最大线程数</li>
     *   <li><strong>activeCount</strong>: 当前活跃线程数</li>
     *   <li><strong>poolSize</strong>: 当前线程池大小</li>
     *   <li><strong>taskCount</strong>: 已提交的任务总数</li>
     *   <li><strong>completedTaskCount</strong>: 已完成的任务数</li>
     *   <li><strong>queueSize</strong>: 当前队列中的任务数</li>
     *   <li><strong>queueRemainingCapacity</strong>: 队列剩余容量</li>
     *   <li><strong>isShutdown</strong>: 是否已关闭</li>
     *   <li><strong>isTerminated</strong>: 是否已终止</li>
     * </ul>
     *
     * <p><strong>使用场景：</strong>
     * <ul>
     *   <li>健康检查接口的数据源</li>
     *   <li>监控系统的指标收集</li>
     *   <li>性能调优的参考数据</li>
     *   <li>问题诊断的辅助信息</li>
     * </ul>
     *
     * @return 包含所有线程池状态信息的Map，键为线程池名称，值为状态详情
     * @see #getHealthStatus() 获取健康状态的简化方法
     */
    public Map<String, Object> getThreadPoolStatus() {
        Map<String, Object> status = new HashMap<>();

        // 收集AI流式处理线程池的状态信息
        if (aiStreamThreadPool != null) {
            Map<String, Object> streamStatus = new HashMap<>();
            streamStatus.put("corePoolSize", aiStreamThreadPool.getCorePoolSize());
            streamStatus.put("maximumPoolSize", aiStreamThreadPool.getMaximumPoolSize());
            streamStatus.put("activeCount", aiStreamThreadPool.getActiveCount());
            streamStatus.put("poolSize", aiStreamThreadPool.getPoolSize());
            streamStatus.put("taskCount", aiStreamThreadPool.getTaskCount());
            streamStatus.put("completedTaskCount", aiStreamThreadPool.getCompletedTaskCount());
            streamStatus.put("queueSize", aiStreamThreadPool.getQueue().size());
            streamStatus.put("queueRemainingCapacity", aiStreamThreadPool.getQueue().remainingCapacity());
            streamStatus.put("isShutdown", aiStreamThreadPool.isShutdown());
            streamStatus.put("isTerminated", aiStreamThreadPool.isTerminated());

            status.put("aiStreamThreadPool", streamStatus);
        }

        return status;
    }

    /**
     * 获取线程池健康状态
     * <p>
     * 基于线程池的关键指标评估整体健康状况，提供简化的状态判断。
     * 该方法通过分析线程使用率和队列使用率来判断系统的健康程度。
     *
     * <p><strong>健康状态分类：</strong>
     * <ul>
     *   <li><strong>HEALTHY</strong>: 系统运行正常，资源使用率在合理范围内</li>
     *   <li><strong>WARNING</strong>: 系统存在潜在风险，需要关注但暂时可用</li>
     *   <li><strong>UNHEALTHY</strong>: 系统不可用或存在严重问题</li>
     * </ul>
     *
     * <p><strong>判断标准：</strong>
     * <ul>
     *   <li>线程池未初始化或已关闭 → UNHEALTHY</li>
     *   <li>线程使用率 > 90% → WARNING</li>
     *   <li>队列使用率 > 80% → WARNING</li>
     *   <li>其他情况 → HEALTHY</li>
     * </ul>
     *
     * <p><strong>使用场景：</strong>
     * <ul>
     *   <li>健康检查端点的快速状态判断</li>
     *   <li>监控告警的触发条件</li>
     *   <li>负载均衡器的健康检查</li>
     *   <li>运维人员的快速状态了解</li>
     * </ul>
     *
     * <p><strong>计算逻辑：</strong>
     * <ul>
     *   <li>线程使用率 = 活跃线程数 / 最大线程数 × 100%</li>
     *   <li>队列使用率 = 队列中任务数 / 队列总容量 × 100%</li>
     * </ul>
     *
     * @return 健康状态字符串，包含状态级别和具体的指标信息
     * @see #getThreadPoolStatus() 获取详细状态信息
     */
    public String getHealthStatus() {
        // 检查线程池是否可用
        if (aiStreamThreadPool == null || aiStreamThreadPool.isShutdown()) {
            return "UNHEALTHY - 线程池未初始化或已关闭";
        }

        // 计算线程使用率
        double threadUtilization = (double) aiStreamThreadPool.getActiveCount() / aiStreamThreadPool.getMaximumPoolSize() * 100;

        // 计算队列使用率
        int queueSize = aiStreamThreadPool.getQueue().size();
        int queueCapacity = queueSize + aiStreamThreadPool.getQueue().remainingCapacity();
        double queueUtilization = queueCapacity > 0 ? (double) queueSize / queueCapacity * 100 : 0;

        // 根据使用率判断健康状态
        if (threadUtilization > 90) {
            return "WARNING - 线程使用率过高: " + String.format("%.1f%%", threadUtilization);
        }

        if (queueUtilization > 80) {
            return "WARNING - 队列使用率过高: " + String.format("%.1f%%", queueUtilization);
        }

        return "HEALTHY";
    }
    
    /**
     * 销毁线程池
     * <p>
     * 在Spring容器关闭时自动调用，负责优雅地关闭所有线程池资源。
     * 该方法确保所有正在执行的任务能够正常完成，并释放相关的系统资源。
     *
     * <p><strong>关闭顺序：</strong>
     * <ol>
     *   <li>关闭AI流式处理线程池</li>
     *   <li>关闭监控定时任务线程池</li>
     * </ol>
     *
     * <p><strong>关闭策略：</strong>
     * <ul>
     *   <li>优雅关闭：首先尝试让正在执行的任务完成</li>
     *   <li>超时控制：设置合理的等待时间，避免无限等待</li>
     *   <li>强制关闭：超时后强制终止，确保资源释放</li>
     *   <li>异常处理：处理关闭过程中可能出现的异常</li>
     * </ul>
     *
     * <p><strong>资源清理：</strong>
     * 确保所有线程池相关的资源都被正确释放，包括线程、队列、监控任务等，
     * 避免内存泄漏和资源占用问题。
     *
     * @see PreDestroy Spring生命周期注解
     * @see #shutdownThreadPool(String, ExecutorService) 单个线程池关闭方法
     */
    @PreDestroy
    public void destroyThreadPools() {
        log.info("开始关闭线程池管理器...");

        // 关闭AI流式处理线程池
        if (aiStreamThreadPool != null) {
            shutdownThreadPool("AI流式处理线程池", aiStreamThreadPool);
        }

        // 关闭监控定时任务线程池
        if (monitorScheduler != null) {
            shutdownThreadPool("监控定时任务线程池", monitorScheduler);
        }

        log.info("线程池管理器已关闭");
    }

    /**
     * 记录线程池状态日志
     * <p>
     * 定期记录线程池的运行状态，支持详细和简化两种日志模式。
     * 该方法由监控调度器定期调用，用于跟踪线程池的性能和健康状况。
     *
     * <p><strong>详细模式日志内容：</strong>
     * <ul>
     *   <li>线程池配置信息：核心线程数、最大线程数、当前线程数</li>
     *   <li>任务执行统计：活跃线程数、任务总数、已完成任务数</li>
     *   <li>队列状态信息：队列大小、队列剩余容量</li>
     *   <li>健康状态评估：基于使用率的健康状态判断</li>
     * </ul>
     *
     * <p><strong>简化模式日志内容：</strong>
     * <ul>
     *   <li>关键指标的一行式摘要</li>
     *   <li>活跃线程数和最大线程数的比例</li>
     *   <li>当前队列大小</li>
     *   <li>已完成的任务总数</li>
     * </ul>
     *
     * <p><strong>日志模式选择：</strong>
     * <ul>
     *   <li>详细模式：适用于问题诊断和性能调优阶段</li>
     *   <li>简化模式：适用于生产环境的日常监控</li>
     * </ul>
     *
     * <p><strong>异常处理：</strong>
     * 如果线程池未初始化，会记录警告日志并提前返回，避免空指针异常。
     *
     * @param detailed 是否记录详细日志。true表示详细模式，false表示简化模式
     * @see #getHealthStatus() 健康状态评估方法
     */
    private void logThreadPoolStatus(boolean detailed) {
        // 检查线程池是否已初始化
        if (aiStreamThreadPool == null) {
            log.warn("AI流式处理线程池未初始化");
            return;
        }

        if (detailed) {
            // 详细模式：记录完整的状态信息
            log.info("=== AI流式处理线程池状态 ===");
            log.info("核心线程数: {}, 最大线程数: {}, 当前线程数: {}",
                    aiStreamThreadPool.getCorePoolSize(),
                    aiStreamThreadPool.getMaximumPoolSize(),
                    aiStreamThreadPool.getPoolSize());
            log.info("活跃线程数: {}, 任务总数: {}, 已完成: {}",
                    aiStreamThreadPool.getActiveCount(),
                    aiStreamThreadPool.getTaskCount(),
                    aiStreamThreadPool.getCompletedTaskCount());
            log.info("队列大小: {}, 队列剩余容量: {}",
                    aiStreamThreadPool.getQueue().size(),
                    aiStreamThreadPool.getQueue().remainingCapacity());
            log.info("健康状态: {}", getHealthStatus());
        } else {
            // 简化模式：记录关键指标的摘要
            log.info("AI流式线程池 - 活跃: {}/{}, 队列: {}, 已完成: {}",
                    aiStreamThreadPool.getActiveCount(),
                    aiStreamThreadPool.getMaximumPoolSize(),
                    aiStreamThreadPool.getQueue().size(),
                    aiStreamThreadPool.getCompletedTaskCount());
        }
    }
    
    /**
     * 关闭单个线程池
     * <p>
     * 实现线程池的优雅关闭，确保正在执行的任务能够完成，同时避免接受新的任务。
     * 该方法采用分阶段的关闭策略，在优雅关闭和强制关闭之间取得平衡。
     *
     * <p><strong>关闭流程：</strong>
     * <ol>
     *   <li><strong>检查状态</strong>：验证线程池是否需要关闭</li>
     *   <li><strong>优雅关闭</strong>：调用shutdown()停止接受新任务</li>
     *   <li><strong>等待完成</strong>：等待30秒让正在执行的任务完成</li>
     *   <li><strong>强制关闭</strong>：超时后调用shutdownNow()强制终止</li>
     *   <li><strong>最终等待</strong>：再等待5秒确保资源释放</li>
     * </ol>
     *
     * <p><strong>超时设置：</strong>
     * <ul>
     *   <li>优雅关闭等待时间：30秒</li>
     *   <li>强制关闭等待时间：5秒</li>
     *   <li>总计最大关闭时间：35秒</li>
     * </ul>
     *
     * <p><strong>异常处理：</strong>
     * <ul>
     *   <li>InterruptedException：当前线程被中断时的处理</li>
     *   <li>恢复中断状态：确保中断信号不丢失</li>
     *   <li>强制关闭：确保即使在异常情况下也能释放资源</li>
     * </ul>
     *
     * <p><strong>日志记录：</strong>
     * <ul>
     *   <li>记录关闭开始和完成的信息</li>
     *   <li>记录超时和强制关闭的警告</li>
     *   <li>记录关闭失败的错误信息</li>
     * </ul>
     *
     * @param name 线程池名称，用于日志记录和问题定位
     * @param executor 要关闭的线程池执行器
     * @see ExecutorService#shutdown() 优雅关闭方法
     * @see ExecutorService#shutdownNow() 强制关闭方法
     * @see ExecutorService#awaitTermination(long, TimeUnit) 等待终止方法
     */
    private void shutdownThreadPool(String name, ExecutorService executor) {
        // 检查线程池状态，避免重复关闭
        if (executor == null || executor.isShutdown()) {
            return;
        }

        log.info("关闭线程池: {}", name);

        try {
            // 第一阶段：优雅关闭，停止接受新任务但允许已提交的任务完成
            executor.shutdown();

            // 第二阶段：等待正在执行的任务完成，最多等待30秒
            if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                log.warn("线程池 {} 在30秒内未能正常关闭，强制关闭", name);

                // 第三阶段：强制关闭，尝试停止正在执行的任务
                executor.shutdownNow();

                // 第四阶段：再次等待，确保强制关闭生效，最多等待5秒
                if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                    log.error("线程池 {} 强制关闭失败", name);
                }
            }

            log.info("线程池 {} 已成功关闭", name);

        } catch (InterruptedException e) {
            // 处理中断异常：当前线程在等待过程中被中断
            log.warn("等待线程池 {} 关闭时被中断，强制关闭", name);
            executor.shutdownNow();
            // 恢复中断状态，确保中断信号不丢失
            Thread.currentThread().interrupt();
        }
    }
}
