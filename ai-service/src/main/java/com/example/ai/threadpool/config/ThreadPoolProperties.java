package com.example.ai.threadpool.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 线程池配置属性
 * 简化的线程池配置管理
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "ai.thread-pool")
public class ThreadPoolProperties {
    
    /**
     * 是否启用线程池管理
     */
    private Boolean enabled = true;
    
    /**
     * AI流式处理线程池配置
     */
    private StreamConfig stream = new StreamConfig();
    
    /**
     * 监控配置
     */
    private MonitorConfig monitor = new MonitorConfig();
    
    /**
     * AI流式处理线程池配置
     */
    @Data
    public static class StreamConfig {
        /**
         * 核心线程数
         */
        private Integer coreSize = 5;
        
        /**
         * 最大线程数
         */
        private Integer maxSize = 20;
        
        /**
         * 线程空闲时间（秒）
         */
        private Integer keepAlive = 60;
        
        /**
         * 队列容量
         */
        private Integer queueCapacity = 100;
        
        /**
         * 线程名称前缀
         */
        private String threadNamePrefix = "ai-stream-chat";
    }
    
    /**
     * 监控配置
     */
    @Data
    public static class MonitorConfig {
        /**
         * 是否启用监控
         */
        private Boolean enabled = true;
        
        /**
         * 监控间隔时间（秒）
         */
        private Integer intervalSeconds = 30;
        
        /**
         * 是否记录详细日志
         */
        private Boolean detailedLogging = false;
    }
}
