package com.example.ai.feign;

import com.example.common.entity.dto.AiChatMessageDto;
import com.example.common.entity.dto.AiChatSessionDto;
import com.example.common.entity.vo.AiChatMessageVo;
import com.example.common.entity.vo.AiChatSessionVo;
import com.example.ai.dto.ChatResponse;
import com.example.common.result.Result;
import com.example.common.feign.fallback.BaseFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Business服务降级工厂
 * 继承BaseFallbackFactory，提供统一的降级处理逻辑
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Component
public class BusinessFeignFallbackFactory extends BaseFallbackFactory<BusinessFeign> {

    public BusinessFeignFallbackFactory() {
        super("business-service");
    }

    @Override
    public BusinessFeign create(Throwable cause) {
        return new BusinessFeign() {

            @Override
            public Result<AiChatSessionVo> createConversation(AiChatSessionDto aiChatSessionDto) {
                logFallback("createConversation", cause);

                // 对于创建操作，返回明确的错误信息
                String errorMessage = extractErrorMessage(cause);
                return Result.error(503, "创建对话失败: " + errorMessage);
            }

            @Override
            public Result updateConversation(AiChatSessionDto aiChatSessionDto) {
                logFallback("updateConversation", cause);

                // 对于更新操作，返回明确的错误信息
                String errorMessage = extractErrorMessage(cause);
                return Result.error(503, "更新对话失败: " + errorMessage);
            }

            @Override
            public List<AiChatSessionVo> getConversations() {
                logFallback("getConversations", cause);

                // 对于列表查询，返回空列表，避免前端显示异常
                // 但在日志中记录详细错误信息供开发者排查
                log.warn("business-service服务不可用，无法获取会话列表，返回空列表");

                return createEmptyList(cause);
            }

            @Override
            public List<AiChatMessageVo> getChatMessageInfo(String sessionId) {
                return List.of();
            }

            @Override
            public String getChatMessageAiInfo(String sessionId) {
                return null;
            }

            @Override
            public void saveChatMessage(AiChatMessageDto aiChatMessageDto) {

            }
        };
    }
}
