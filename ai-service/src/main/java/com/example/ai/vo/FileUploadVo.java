package com.example.ai.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 文件上传响应VO
 * 用于返回文件上传结果
 * 
 * <AUTHOR> Service
 * @date 2025-07-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class FileUploadVo {

    /**
     * 文件唯一标识
     */
    private String fileId;

    /**
     * 原始文件名
     */
    private String originalFileName;

    /**
     * 存储文件名
     */
    private String storedFileName;

    /**
     * 文件扩展名
     */
    private String fileExtension;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件大小（格式化显示）
     */
    private String fileSizeFormatted;

    /**
     * 文件HTTP访问URL
     */
    private String fileUrl;

    /**
     * 文件类型
     */
    private String contentType;

    /**
     * 文件分类
     */
    private String category;

    /**
     * 文件描述
     */
    private String description;

    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;

    /**
     * 上传用户ID
     */
    private Long userId;

    /**
     * 缩略图URL（如果生成了缩略图）
     */
    private String thumbnailUrl;
}
