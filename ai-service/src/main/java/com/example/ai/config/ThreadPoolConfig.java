package com.example.ai.config;

import com.example.ai.threadpool.config.ThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 线程池配置类
 * 启用线程池配置属性
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(ThreadPoolProperties.class)
public class ThreadPoolConfig {
    
    public ThreadPoolConfig() {
        log.info("线程池配置已启用");
    }
}
