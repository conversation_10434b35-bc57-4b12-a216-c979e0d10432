package com.example.ai.config;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;



/**
 * AOP配置类
 * 确保AOP功能正确启用
 */
@Slf4j
@Configuration
@EnableAspectJAutoProxy(proxyTargetClass = true)  // 强制使用CGLIB代理
public class AopConfig {

    @PostConstruct
    public void init() {
        log.info("=== AOP配置类已初始化，AspectJ自动代理已启用 ===");
        System.out.println("=== AOP配置类已初始化，AspectJ自动代理已启用 ===");
    }
}
