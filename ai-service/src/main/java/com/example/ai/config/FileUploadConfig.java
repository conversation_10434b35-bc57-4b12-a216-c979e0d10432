package com.example.ai.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

/**
 * 文件上传配置类
 * 用于配置文件上传的相关参数
 *
 * <AUTHOR> Service
 * @date 2025-07-24
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "file.upload")
public class FileUploadConfig {

    /**
     * 文件上传路径
     */
    private String path = "./uploads/";

    /**
     * 访问URL前缀
     */
    private String urlPrefix = "http://localhost:8083/files/";

    /**
     * 允许的文件类型（逗号分隔）
     */
    private String allowedTypes = "jpg,jpeg,png,gif,bmp,webp";

    /**
     * 单个文件最大大小（MB）
     */
    private Integer maxFileSize = 10;

    /**
     * 是否启用文件上传功能
     */
    private Boolean enabled = true;

    /**
     * 获取允许的文件类型列表
     *
     * @return 文件类型列表
     */
    public List<String> getAllowedTypesList() {
        return Arrays.asList(allowedTypes.toLowerCase().split(","));
    }

    /**
     * 检查文件类型是否被允许
     *
     * @param fileExtension 文件扩展名
     * @return true表示允许，false表示不允许
     */
    public boolean isAllowedType(String fileExtension) {
        if (fileExtension == null || fileExtension.trim().isEmpty()) {
            return false;
        }
        return getAllowedTypesList().contains(fileExtension.toLowerCase().trim());
    }

    /**
     * 获取最大文件大小（字节）
     *
     * @return 最大文件大小（字节）
     */
    public long getMaxFileSizeBytes() {
        return maxFileSize * 1024L * 1024L;
    }
}
