package com.example.user.feign;

import com.example.common.result.Result;
import com.example.common.feign.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 认证服务Feign客户端
 * 用于调用auth-service的相关接口
 * 使用统一的Feign配置和降级策略
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@FeignClient(
    name = "auth-service",
    configuration = FeignConfig.class,
    fallbackFactory = AuthFeignFallbackFactory.class
)
public interface AuthFeign {

    /**
     * 验证JWT Token
     * 
     * @param token JWT Token
     * @return 验证结果
     */
    @PostMapping("/auth/validate")
    Result<Boolean> validateToken(@RequestParam("token") String token);

    /**
     * 获取用户权限信息
     * 
     * @param userId 用户ID
     * @return 权限信息
     */
    @GetMapping("/auth/permissions")
    Result<Object> getUserPermissions(@RequestParam("userId") Long userId);

    /**
     * 刷新Token
     * 
     * @param refreshToken 刷新Token
     * @return 新的Token信息
     */
    @PostMapping("/auth/refresh")
    Result<Object> refreshToken(@RequestParam("refreshToken") String refreshToken);
}
