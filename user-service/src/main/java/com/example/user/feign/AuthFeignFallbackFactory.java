package com.example.user.feign;

import com.example.common.result.Result;
import com.example.common.feign.fallback.BaseFallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 认证服务降级工厂
 * 继承BaseFallbackFactory，提供统一的降级处理逻辑
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Component
public class AuthFeignFallbackFactory extends BaseFallbackFactory<AuthFeign> {

    public AuthFeignFallbackFactory() {
        super("auth-service");
    }

    @Override
    public AuthFeign create(Throwable cause) {
        return new AuthFeign() {
            
            @Override
            public Result<Boolean> validateToken(String token) {
                logFallback("validateToken", cause);
                
                // Token验证失败时，为了安全考虑，返回验证失败
                // 这样可以确保在认证服务不可用时，系统仍然是安全的
                return Result.error(503, "认证服务暂时不可用，请稍后重试");
            }

            @Override
            public Result<Object> getUserPermissions(Long userId) {
                logFallback("getUserPermissions", cause);
                
                // 权限获取失败时，返回错误信息
                // 避免返回空权限导致安全问题
                return createErrorResult(cause);
            }

            @Override
            public Result<Object> refreshToken(String refreshToken) {
                logFallback("refreshToken", cause);
                
                // Token刷新失败时，返回明确的错误信息
                String errorMessage = extractErrorMessage(cause);
                return Result.error(503, "Token刷新失败: " + errorMessage);
            }
        };
    }
}
