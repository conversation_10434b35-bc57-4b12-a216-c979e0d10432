package com.example.user.service.impl;

import com.example.user.entity.User;
import com.example.user.mapper.UserMapper;
import com.example.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;

    @Override
    public User getUserById(Long id) {
        log.info("Getting user by id: {}", id);
        return userMapper.selectById(id);
    }

    @Override
    public List<User> getAllUsers() {
        log.info("Getting all users");
        return userMapper.selectList(null);
    }

    @Override
    public void updateUser(User user) {
        log.info("Updating user: {}", user.getId());
        userMapper.updateById(user);
    }

    @Override
    public void deleteUser(Long id) {
        log.info("Deleting user: {}", id);
        userMapper.deleteById(id);
    }

    @Override
    public User getUserByUsername(String username) {
        log.info("Getting user by username: {}", username);
        return userMapper.selectByUsername(username);
    }
}
