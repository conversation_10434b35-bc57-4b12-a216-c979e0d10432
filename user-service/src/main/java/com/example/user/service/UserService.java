package com.example.user.service;

import com.example.user.entity.User;

import java.util.List;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 根据ID获取用户
     */
    User getUserById(Long id);

    /**
     * 获取所有用户
     */
    List<User> getAllUsers();

    /**
     * 更新用户信息
     */
    void updateUser(User user);

    /**
     * 删除用户
     */
    void deleteUser(Long id);

    /**
     * 根据用户名获取用户
     */
    User getUserByUsername(String username);
}
