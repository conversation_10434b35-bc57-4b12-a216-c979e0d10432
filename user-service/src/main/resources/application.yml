# ==========================================
# 用户管理服务配置文件
# 负责用户信息的CRUD操作和用户相关业务
# ==========================================

# 服务器配置
server:
  port: 8082  # 用户服务端口
  servlet:
    context-path: # 应用上下文路径
    encoding:
      charset: UTF-8  # 字符编码
      enabled: true
      force: true

# Spring框架配置
spring:
  application:
    name: user-service  # 服务名称，用于服务注册和发现

  # Spring Cloud配置
  cloud:
    # Nacos服务注册与发现配置
    nacos:
      discovery:
        server-addr: ************:8848  # Nacos服务器地址（线上环境）
        namespace: # 命名空间，用于环境隔离
        group: DEFAULT_GROUP  # 服务分组
        cluster-name: DEFAULT  # 集群名称
        metadata:  # 服务元数据
          version: 1.0.0
          description: "用户信息管理服务"
          contact: "<EMAIL>"
      config:
        server-addr: ************:8848  # Nacos配置中心地址（线上环境）
        file-extension: yml  # 配置文件扩展名
        namespace: # 配置命名空间
        group: DEFAULT_GROUP  # 配置分组
        refresh-enabled: true  # 启用配置自动刷新

  # 数据源配置 - MySQL数据库
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver  # MySQL 8.x驱动
    url: *****************************************************************************************************************************************************
    username: root  # 数据库用户名
    password: root  # 数据库密码
    # HikariCP连接池配置
    hikari:
      maximum-pool-size: 20  # 连接池最大连接数
      minimum-idle: 5  # 连接池最小空闲连接数
      connection-timeout: 30000  # 连接超时时间(毫秒)
      idle-timeout: 600000  # 空闲连接超时时间(毫秒)
      max-lifetime: 1800000  # 连接最大生存时间(毫秒)
      leak-detection-threshold: 60000  # 连接泄漏检测阈值(毫秒)

  # Redis配置 - 用于缓存用户信息
  data:
    redis:
      host: localhost  # Redis服务器地址
      port: 6379  # Redis端口
      database: 2  # 使用数据库2，避免与其他服务冲突
      password:  # Redis密码，如果设置了密码请填写
      timeout: 10000ms  # 连接超时时间
      # Lettuce连接池配置
      lettuce:
        pool:
          max-active: 8  # 连接池最大连接数
          max-wait: -1ms  # 连接池最大阻塞等待时间
          max-idle: 8  # 连接池最大空闲连接数
          min-idle: 0  # 连接池最小空闲连接数
        shutdown-timeout: 100ms  # 关闭超时时间

# MyBatis Plus配置 - ORM框架配置
mybatis-plus:
  # MyBatis配置
  configuration:
    map-underscore-to-camel-case: true  # 下划线转驼峰命名
    cache-enabled: true  # 启用二级缓存
    # 移除SQL日志输出，减少控制台日志量
    # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # SQL日志输出到控制台
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler  # 枚举类型处理器
    lazy-loading-enabled: true  # 启用延迟加载
    aggressive-lazy-loading: false  # 禁用积极的延迟加载
  # 全局配置
  global-config:
    # 数据库配置
    db-config:
      id-type: ASSIGN_ID  # 主键生成策略：雪花算法
      logic-delete-field: deleted  # 逻辑删除字段名
      logic-delete-value: 1  # 逻辑删除值(已删除)
      logic-not-delete-value: 0  # 逻辑删除值(未删除)
      table-underline: true  # 表名下划线命名
  # Mapper XML文件位置
  mapper-locations: classpath*:mapper/*.xml
  # 实体类包路径
  type-aliases-package: com.example.user.entity

# OpenFeign配置 - 微服务间调用（统一配置）
feign:
  client:
    config:
      default:  # 默认配置，应用于所有Feign客户端
        connect-timeout: 5000  # 连接超时时间(毫秒)
        read-timeout: 10000  # 读取超时时间(毫秒)
        logger-level: basic  # 日志级别：NONE, BASIC, HEADERS, FULL
      auth-service:  # 针对认证服务的特殊配置
        connect-timeout: 3000  # 认证服务连接超时
        read-timeout: 8000  # 认证服务读取超时
        logger-level: basic
  # 启用压缩
  compression:
    request:
      enabled: true  # 启用请求压缩
      mime-types: text/xml,application/xml,application/json  # 压缩的MIME类型
      min-request-size: 2048  # 最小压缩请求大小
    response:
      enabled: true  # 启用响应压缩
  # 启用Circuit Breaker支持
  circuitbreaker:
    enabled: true  # 启用Circuit Breaker以支持fallback功能
    alphanumeric-ids:
      enabled: true  # 启用字母数字ID
  # 禁用Hystrix，使用Resilience4j替代
  hystrix:
    enabled: false

# Resilience4j配置 - 熔断器、重试、限流等
resilience4j:
  # 熔断器配置
  circuitbreaker:
    configs:
      default:
        failure-rate-threshold: 50  # 失败率阈值50%
        minimum-number-of-calls: 10  # 最小调用次数
        sliding-window-size: 20  # 滑动窗口大小
        wait-duration-in-open-state: 30s  # 熔断器打开状态等待时间
        permitted-number-of-calls-in-half-open-state: 5  # 半开状态允许的调用次数
        automatic-transition-from-open-to-half-open-enabled: true  # 自动从打开状态转换到半开状态
    instances:
      auth-service:
        base-config: default
        failure-rate-threshold: 40  # 认证服务失败率阈值（更严格）

  # 重试配置
  retry:
    configs:
      default:
        max-attempts: 3  # 最大重试次数
        wait-duration: 1s  # 重试间隔
        retry-exceptions:
          - java.net.ConnectException
          - java.net.SocketTimeoutException
          - org.springframework.web.client.ResourceAccessException
    instances:
      auth-service:
        base-config: default
        max-attempts: 2  # 认证服务重试次数较少

# 日志配置 - 简化版本，减少冗余信息
logging:
  level:
    # 根日志级别设置为INFO
    root: info
    # 用户服务核心包使用INFO级别
    com.example.user: info  # 自定义包的日志级别
    # 框架日志设置为WARN级别
    org.springframework.cloud.openfeign: warn  # OpenFeign日志级别
    org.mybatis: warn  # MyBatis日志级别
    com.baomidou.mybatisplus: warn  # MyBatis Plus日志级别
    # 数据库连接池日志设置为ERROR级别
    com.zaxxer.hikari: error
    # Nacos日志设置为WARN级别
    com.alibaba.nacos: warn
  pattern:
    # 简化的日志输出格式
    console: "%d{HH:mm:ss.SSS} %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %logger{50} - %msg%n"
  file:
    name: logs/user-service.log  # 日志文件路径
    max-size: 50MB  # 减小单个日志文件大小
    max-history: 15  # 减少保留的日志文件数量

# 管理端点配置 - 用于健康检查和监控
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus  # 暴露的端点
  endpoint:
    health:
      show-details: always  # 显示健康检查详情
  metrics:
    export:
      prometheus:
        enabled: true  # 启用Prometheus指标导出
