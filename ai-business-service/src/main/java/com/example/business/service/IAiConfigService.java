package com.example.business.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.business.entity.AiConfig;

/**
 * AI配置Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IAiConfigService extends IService<AiConfig>
{
    /**
     * 查询AI配置
     *
     * @param id AI配置主键
     * @return AI配置
     */
    public AiConfig selectAiConfigById(Long id);

    /**
     * 查询AI配置列表
     *
     * @param aiConfig AI配置
     * @return AI配置集合
     */
    public List<AiConfig> selectAiConfigList(AiConfig aiConfig);

    /**
     * 新增AI配置
     *
     * @param aiConfig AI配置
     * @return 结果
     */
    public int insertAiConfig(AiConfig aiConfig);

    /**
     * 修改AI配置
     *
     * @param aiConfig AI配置
     * @return 结果
     */
    public int updateAiConfig(AiConfig aiConfig);

    /**
     * 批量删除AI配置
     *
     * @param ids 需要删除的AI配置主键集合
     * @return 结果
     */
    public int deleteAiConfigByIds(Long[] ids);

    /**
     * 删除AI配置信息
     *
     * @param id AI配置主键
     * @return 结果
     */
    public int deleteAiConfigById(Long id);
}
