package com.example.business.service.impl;

import java.util.List;
import com.example.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.example.business.mapper.AiUsageSummaryMapper;
import com.example.business.entity.AiUsageSummary;
import com.example.business.service.IAiUsageSummaryService;

/**
 * AI使用汇总统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
public class AiUsageSummaryServiceImpl implements IAiUsageSummaryService
{
    @Autowired
    private AiUsageSummaryMapper aiUsageSummaryMapper;

    /**
     * 查询AI使用汇总统计
     *
     * @param id AI使用汇总统计主键
     * @return AI使用汇总统计
     */
    @Override
    public AiUsageSummary selectAiUsageSummaryById(Long id)
    {
        return aiUsageSummaryMapper.selectAiUsageSummaryById(id);
    }

    /**
     * 查询AI使用汇总统计列表
     *
     * @param aiUsageSummary AI使用汇总统计
     * @return AI使用汇总统计
     */
    @Override
    public List<AiUsageSummary> selectAiUsageSummaryList(AiUsageSummary aiUsageSummary)
    {
        return aiUsageSummaryMapper.selectAiUsageSummaryList(aiUsageSummary);
    }

    /**
     * 新增AI使用汇总统计
     *
     * @param aiUsageSummary AI使用汇总统计
     * @return 结果
     */
    @Override
    public int insertAiUsageSummary(AiUsageSummary aiUsageSummary)
    {
        aiUsageSummary.setCreateTime(DateUtils.getNowDate());
        return aiUsageSummaryMapper.insertAiUsageSummary(aiUsageSummary);
    }

    /**
     * 修改AI使用汇总统计
     *
     * @param aiUsageSummary AI使用汇总统计
     * @return 结果
     */
    @Override
    public int updateAiUsageSummary(AiUsageSummary aiUsageSummary)
    {
        aiUsageSummary.setUpdateTime(DateUtils.getNowDate());
        return aiUsageSummaryMapper.updateAiUsageSummary(aiUsageSummary);
    }

    /**
     * 批量删除AI使用汇总统计
     *
     * @param ids 需要删除的AI使用汇总统计主键
     * @return 结果
     */
    @Override
    public int deleteAiUsageSummaryByIds(Long[] ids)
    {
        return aiUsageSummaryMapper.deleteAiUsageSummaryByIds(ids);
    }

    /**
     * 删除AI使用汇总统计信息
     *
     * @param id AI使用汇总统计主键
     * @return 结果
     */
    @Override
    public int deleteAiUsageSummaryById(Long id)
    {
        return aiUsageSummaryMapper.deleteAiUsageSummaryById(id);
    }
}
