package com.example.business.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.example.business.mapper.AiModelConfigMapper;
import com.example.business.entity.AiModelConfig;
import com.example.business.service.IAiModelConfigService;

/**
 * AI模型配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
public class AiModelConfigServiceImpl extends ServiceImpl<AiModelConfigMapper,AiModelConfig> implements IAiModelConfigService
{
    @Autowired
    private AiModelConfigMapper aiModelConfigMapper;

    /**
     * 查询AI模型配置
     *
     * @param id AI模型配置主键
     * @return AI模型配置
     */
    @Override
    public AiModelConfig selectAiModelConfigById(Long id)
    {
        return aiModelConfigMapper.selectAiModelConfigById(id);
    }

    /**
     * 查询AI模型配置列表
     *
     * @param aiModelConfig AI模型配置
     * @return AI模型配置
     */
    @Override
    public List<AiModelConfig> selectAiModelConfigList(AiModelConfig aiModelConfig)
    {
        return aiModelConfigMapper.selectAiModelConfigList(aiModelConfig);
    }

    /**
     * 新增AI模型配置
     *
     * @param aiModelConfig AI模型配置
     * @return 结果
     */
    @Override
    public int insertAiModelConfig(AiModelConfig aiModelConfig)
    {
        aiModelConfig.setCreateTime(DateUtils.getNowDate());
        return aiModelConfigMapper.insertAiModelConfig(aiModelConfig);
    }

    /**
     * 修改AI模型配置
     *
     * @param aiModelConfig AI模型配置
     * @return 结果
     */
    @Override
    public int updateAiModelConfig(AiModelConfig aiModelConfig)
    {
        aiModelConfig.setUpdateTime(DateUtils.getNowDate());
        return aiModelConfigMapper.updateAiModelConfig(aiModelConfig);
    }

    /**
     * 批量删除AI模型配置
     *
     * @param ids 需要删除的AI模型配置主键
     * @return 结果
     */
    @Override
    public int deleteAiModelConfigByIds(Long[] ids)
    {
        return aiModelConfigMapper.deleteAiModelConfigByIds(ids);
    }

    /**
     * 删除AI模型配置信息
     *
     * @param id AI模型配置主键
     * @return 结果
     */
    @Override
    public int deleteAiModelConfigById(Long id)
    {
        return aiModelConfigMapper.deleteAiModelConfigById(id);
    }
}
