package com.example.business.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.business.entity.AiChatTemplate;
import com.example.common.entity.vo.AiChatTemplateVo;

/**
 * AI会话模板Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IAiChatTemplateService extends IService<AiChatTemplate>
{
    /**
     * 查询AI会话模板
     *
     * @param id AI会话模板主键
     * @return AI会话模板
     */
    public AiChatTemplate selectAiChatTemplateById(Long id);

    /**
     * 查询AI会话模板列表
     *
     * @param aiChatTemplate AI会话模板
     * @return AI会话模板集合
     */
    public List<AiChatTemplate> selectAiChatTemplateList(AiChatTemplate aiChatTemplate);

    /**
     * 新增AI会话模板
     *
     * @param aiChatTemplate AI会话模板
     * @return 结果
     */
    public int insertAiChatTemplate(AiChatTemplate aiChatTemplate);

    /**
     * 修改AI会话模板
     *
     * @param aiChatTemplate AI会话模板
     * @return 结果
     */
    public int updateAiChatTemplate(AiChatTemplate aiChatTemplate);

    /**
     * 批量删除AI会话模板
     *
     * @param ids 需要删除的AI会话模板主键集合
     * @return 结果
     */
    public int deleteAiChatTemplateByIds(Long[] ids);

    /**
     * 删除AI会话模板信息
     *
     * @param id AI会话模板主键
     * @return 结果
     */
    public int deleteAiChatTemplateById(Long id);

    /**
     * 获取缓存中的AI会话模板列表
     * @return
     */
    List<AiChatTemplateVo> getCacheList();
}
