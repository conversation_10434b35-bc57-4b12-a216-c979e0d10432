package com.example.business.service;

import java.util.List;
import com.example.business.entity.AiTokenUsage;

/**
 * AI Token使用统计Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IAiTokenUsageService
{
    /**
     * 查询AI Token使用统计
     *
     * @param id AI Token使用统计主键
     * @return AI Token使用统计
     */
    public AiTokenUsage selectAiTokenUsageById(Long id);

    /**
     * 查询AI Token使用统计列表
     *
     * @param aiTokenUsage AI Token使用统计
     * @return AI Token使用统计集合
     */
    public List<AiTokenUsage> selectAiTokenUsageList(AiTokenUsage aiTokenUsage);

    /**
     * 新增AI Token使用统计
     *
     * @param aiTokenUsage AI Token使用统计
     * @return 结果
     */
    public int insertAiTokenUsage(AiTokenUsage aiTokenUsage);

    /**
     * 修改AI Token使用统计
     *
     * @param aiTokenUsage AI Token使用统计
     * @return 结果
     */
    public int updateAiTokenUsage(AiTokenUsage aiTokenUsage);

    /**
     * 批量删除AI Token使用统计
     *
     * @param ids 需要删除的AI Token使用统计主键集合
     * @return 结果
     */
    public int deleteAiTokenUsageByIds(Long[] ids);

    /**
     * 删除AI Token使用统计信息
     *
     * @param id AI Token使用统计主键
     * @return 结果
     */
    public int deleteAiTokenUsageById(Long id);
}
