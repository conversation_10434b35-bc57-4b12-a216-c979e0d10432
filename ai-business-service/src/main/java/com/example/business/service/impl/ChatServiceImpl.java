package com.example.business.service.impl;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.business.entity.dto.ChatRequest;
import com.example.business.entity.dto.ChatResponse;
import com.example.business.entity.Chat;
import com.example.business.mapper.ChatMapper;
import com.example.business.service.ChatService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class ChatServiceImpl extends ServiceImpl<ChatMapper, Chat> implements ChatService {
    @Resource
    private ChatMapper chatMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ChatResponse createConversation(ChatRequest chatRequest) {
        // 参数验证
        if (chatRequest == null) {
            throw new IllegalArgumentException("聊天请求参数不能为空");
        }
        if (!StringUtils.hasText(chatRequest.getTitle())) {
            throw new IllegalArgumentException("会话标题不能为空");
        }
        if (chatRequest.getUserId() == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }

        try {
            // 生成唯一会话ID
            String chatId = UUID.randomUUID().toString();
            log.info("开始创建新会话，chatId: {}, title: {}, userId: {}",
                    chatId, chatRequest.getTitle(), chatRequest.getUserId());

            // 构建会话实体
            Chat chat = new Chat()
                    .setChatId(chatId)
                    .setChatTitle(chatRequest.getTitle())
                    .setUserId(chatRequest.getUserId()); // 使用请求中的用户ID而不是硬编码

            // 保存到数据库
            int insertResult = chatMapper.insert(chat);
            if (insertResult <= 0) {
                throw new RuntimeException("会话创建失败，数据库插入异常");
            }

            log.info("新建会话创建成功，chatId: {}, insertResult: {}", chatId, insertResult);

            // 构建响应对象
            Date now = new Date();
            ChatResponse chatResponse = new ChatResponse()
                    .setId(chatId)
                    .setTitle(chatRequest.getTitle())
                    .setUserId(chatRequest.getUserId())
                    .setModel(chatRequest.getModel())
                    .setSessionId(chatRequest.getSessionId())
                    .setCreatedAt(now)
                    .setUpdatedAt(now);

            log.info("会话响应构建完成，chatId: {}", chatId);
            return chatResponse;

        } catch (Exception e) {
            log.error("创建会话失败，title: {}, userId: {}, error: {}",
                    chatRequest.getTitle(), chatRequest.getUserId(), e.getMessage(), e);
            throw new RuntimeException("创建会话失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<ChatResponse> getConversations() {
        try {
            log.info("开始获取会话列表");
            List<ChatResponse> conversations = chatMapper.getConversations();
            log.info("成功获取会话列表，数量: {}", conversations != null ? conversations.size() : 0);
            return conversations;
        } catch (Exception e) {
            log.error("获取会话列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取会话列表失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateConversationTitle(String conversationId, String title) {
        // 参数验证
        if (!StringUtils.hasText(conversationId)) {
            throw new IllegalArgumentException("会话ID不能为空");
        }
        if (!StringUtils.hasText(title)) {
            throw new IllegalArgumentException("会话标题不能为空");
        }

        try {
            log.info("开始更新会话标题，conversationId: {}, title: {}", conversationId, title);

            boolean update = new LambdaUpdateChainWrapper<>(chatMapper)
                    .eq(Chat::getChatId, conversationId)
                    .set(Chat::getChatTitle, title)
                    .set(Chat::getUpdateTime, new Date()) // 更新修改时间
                    .update();

            if (update) {
                log.info("会话标题更新成功，conversationId: {}, newTitle: {}", conversationId, title);
            } else {
                log.error("会话标题更新失败，可能会话不存在，conversationId: {}", conversationId);
                throw new RuntimeException("会话标题更新失败，会话可能不存在");
            }
        } catch (Exception e) {
            log.error("更新会话标题异常，conversationId: {}, title: {}, error: {}",
                    conversationId, title, e.getMessage(), e);
            throw new RuntimeException("更新会话标题失败: " + e.getMessage(), e);
        }
    }
}
