package com.example.business.service.impl;

import java.util.List;
import com.example.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.example.business.mapper.AiTokenUsageMapper;
import com.example.business.entity.AiTokenUsage;
import com.example.business.service.IAiTokenUsageService;

/**
 * AI Token使用统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
public class AiTokenUsageServiceImpl implements IAiTokenUsageService
{
    @Autowired
    private AiTokenUsageMapper aiTokenUsageMapper;

    /**
     * 查询AI Token使用统计
     *
     * @param id AI Token使用统计主键
     * @return AI Token使用统计
     */
    @Override
    public AiTokenUsage selectAiTokenUsageById(Long id)
    {
        return aiTokenUsageMapper.selectAiTokenUsageById(id);
    }

    /**
     * 查询AI Token使用统计列表
     *
     * @param aiTokenUsage AI Token使用统计
     * @return AI Token使用统计
     */
    @Override
    public List<AiTokenUsage> selectAiTokenUsageList(AiTokenUsage aiTokenUsage)
    {
        return aiTokenUsageMapper.selectAiTokenUsageList(aiTokenUsage);
    }

    /**
     * 新增AI Token使用统计
     *
     * @param aiTokenUsage AI Token使用统计
     * @return 结果
     */
    @Override
    public int insertAiTokenUsage(AiTokenUsage aiTokenUsage)
    {
        aiTokenUsage.setCreateTime(DateUtils.getNowDate());
        return aiTokenUsageMapper.insertAiTokenUsage(aiTokenUsage);
    }

    /**
     * 修改AI Token使用统计
     *
     * @param aiTokenUsage AI Token使用统计
     * @return 结果
     */
    @Override
    public int updateAiTokenUsage(AiTokenUsage aiTokenUsage)
    {
        aiTokenUsage.setUpdateTime(DateUtils.getNowDate());
        return aiTokenUsageMapper.updateAiTokenUsage(aiTokenUsage);
    }

    /**
     * 批量删除AI Token使用统计
     *
     * @param ids 需要删除的AI Token使用统计主键
     * @return 结果
     */
    @Override
    public int deleteAiTokenUsageByIds(Long[] ids)
    {
        return aiTokenUsageMapper.deleteAiTokenUsageByIds(ids);
    }

    /**
     * 删除AI Token使用统计信息
     *
     * @param id AI Token使用统计主键
     * @return 结果
     */
    @Override
    public int deleteAiTokenUsageById(Long id)
    {
        return aiTokenUsageMapper.deleteAiTokenUsageById(id);
    }
}
