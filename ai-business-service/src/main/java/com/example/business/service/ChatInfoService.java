package com.example.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.business.entity.dto.ChatInfoRequest;
import com.example.business.entity.dto.ChatInfoResponse;
import com.example.business.entity.ChatInfo;

import java.util.List;


public interface ChatInfoService extends IService<ChatInfo> {

    /**
     * 根据会话id获取会话详细列表信息
     * @param chatId
     * @return
     */
    List<ChatInfoResponse> getByChatId(String chatId);

    /**
     * 保存会话详情
     * @param chatInfoRequest
     */
    void saveChat(ChatInfoRequest chatInfoRequest);

    List<ChatInfoResponse> getByChatIdAndType(String chatId);
}
