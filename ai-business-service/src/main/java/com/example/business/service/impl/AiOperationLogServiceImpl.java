package com.example.business.service.impl;

import java.util.List;

import cn.hutool.core.convert.impl.BeanConverter;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.common.entity.dto.AiOperationLogDto;
import com.example.common.utils.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.example.business.mapper.AiOperationLogMapper;
import com.example.business.entity.AiOperationLog;
import com.example.business.service.IAiOperationLogService;

/**
 * AI系统操作日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
public class AiOperationLogServiceImpl extends ServiceImpl<AiOperationLogMapper,AiOperationLog> implements IAiOperationLogService
{
    @Autowired
    private AiOperationLogMapper aiOperationLogMapper;

    /**
     * 查询AI系统操作日志
     *
     * @param id AI系统操作日志主键
     * @return AI系统操作日志
     */
    @Override
    public AiOperationLog selectAiOperationLogById(Long id)
    {
        return aiOperationLogMapper.selectAiOperationLogById(id);
    }

    /**
     * 查询AI系统操作日志列表
     *
     * @param aiOperationLog AI系统操作日志
     * @return AI系统操作日志
     */
    @Override
    public List<AiOperationLog> selectAiOperationLogList(AiOperationLog aiOperationLog)
    {
        return aiOperationLogMapper.selectAiOperationLogList(aiOperationLog);
    }

    /**
     * 新增AI系统操作日志
     *
     * @param aiOperationLogDto AI系统操作日志
     * @return 结果
     */
    @Override
    public int insertAiOperationLog(AiOperationLogDto aiOperationLogDto)
    {
        AiOperationLog aiOperationLog = new AiOperationLog();
        //属性复制
        BeanUtils.copyProperties(aiOperationLogDto, aiOperationLog); //属性值复制
        return aiOperationLogMapper.insert(aiOperationLog);
    }

    /**
     * 修改AI系统操作日志
     *
     * @param aiOperationLog AI系统操作日志
     * @return 结果
     */
    @Override
    public int updateAiOperationLog(AiOperationLog aiOperationLog)
    {
        return aiOperationLogMapper.updateAiOperationLog(aiOperationLog);
    }

    /**
     * 批量删除AI系统操作日志
     *
     * @param ids 需要删除的AI系统操作日志主键
     * @return 结果
     */
    @Override
    public int deleteAiOperationLogByIds(Long[] ids)
    {
        return aiOperationLogMapper.deleteAiOperationLogByIds(ids);
    }

    /**
     * 删除AI系统操作日志信息
     *
     * @param id AI系统操作日志主键
     * @return 结果
     */
    @Override
    public int deleteAiOperationLogById(Long id)
    {
        return aiOperationLogMapper.deleteAiOperationLogById(id);
    }
}
