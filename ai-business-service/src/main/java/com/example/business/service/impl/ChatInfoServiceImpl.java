package com.example.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.business.entity.dto.ChatInfoRequest;
import com.example.business.entity.dto.ChatInfoResponse;
import com.example.business.entity.ChatInfo;
import com.example.business.mapper.ChatInfoMapper;
import com.example.business.service.ChatInfoService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ChatInfoServiceImpl extends ServiceImpl<ChatInfoMapper, ChatInfo> implements ChatInfoService {
    @Resource
    private ChatInfoMapper chatInfoMapper;


    @Override
    public List<ChatInfoResponse> getByChatId(String chatId) {
        List<ChatInfoResponse> chatInfoResponseList = chatInfoMapper.getByChatId(chatId);
        return chatInfoResponseList;
    }

    @Override
    public void saveChat(ChatInfoRequest chatInfoRequest) {

        //持久化保存对话内容(AI)
        ChatInfo chatInfo = new ChatInfo()
                .setChatId(chatInfoRequest.getChatId())
                .setAiResponse(chatInfoRequest.getAiResponse())
                .setChatInfoContent(chatInfoRequest.getChatInfoContent())
                .setType("ai");


        //持久化保存对话内容(user)
        ChatInfo chatInfoUser = new ChatInfo()
                .setChatId(chatInfoRequest.getChatId())
                .setChatInfoTitle(chatInfoRequest.getChatInfoTitle())
                .setChatInfoContent(chatInfoRequest.getChatInfoTitle())
                .setType("user");


        chatInfoMapper.insert(chatInfoUser);
        chatInfoMapper.insert(chatInfo);


    }

    @Override
    public List<ChatInfoResponse> getByChatIdAndType(String chatId) {
        return chatInfoMapper.getByChatIdAndType(chatId);
    }
}
