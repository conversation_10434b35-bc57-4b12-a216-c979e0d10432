package com.example.business.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.business.entity.AiChatMessage;
import com.example.common.entity.dto.AiChatMessageDto;
import com.example.common.entity.vo.AiChatMessageVo;

/**
 * AI聊天消息Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IAiChatMessageService extends IService<AiChatMessage>
{
    /**
     * 查询AI聊天消息
     *
     * @param id AI聊天消息主键
     * @return AI聊天消息
     */
    public AiChatMessage selectAiChatMessageById(Long id);

    /**
     * 查询AI聊天消息列表
     *
     * @param aiChatMessageDto AI聊天消息
     * @return AI聊天消息集合
     */
    public List<AiChatMessage> selectAiChatMessageList(AiChatMessageDto aiChatMessageDto);

    /**
     * 新增AI聊天消息
     *
     * @param aiChatMessageDto AI聊天消息
     * @return 结果
     */
    public void insertAiChatMessage(AiChatMessageDto aiChatMessageDto);

    /**
     * 修改AI聊天消息
     *
     * @param aiChatMessageDto AI聊天消息
     * @return 结果
     */
    public int updateAiChatMessage(AiChatMessageDto aiChatMessageDto);

    /**
     * 批量删除AI聊天消息
     *
     * @param ids 需要删除的AI聊天消息主键集合
     * @return 结果
     */
    public int deleteAiChatMessageByIds(Long[] ids);

    /**
     * 删除AI聊天消息信息
     *
     * @param id AI聊天消息主键
     * @return 结果
     */
    public int deleteAiChatMessageById(Long id);


    /**
     * 根据会话id获取会话列表
     * @param sessionId
     * @return
     */
    List<AiChatMessageVo> selectBySessionId(String sessionId);


    List<AiChatMessageVo> selectByAiInfo(String sessionId);
}
