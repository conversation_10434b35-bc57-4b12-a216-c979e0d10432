package com.example.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.business.entity.dto.ChatRequest;
import com.example.business.entity.dto.ChatResponse;
import com.example.business.entity.Chat;
import jakarta.validation.constraints.NotBlank;

import java.util.List;


public interface ChatService extends IService<Chat> {
    /**
     * 创建新对话
     * @param chatRequest
     * @return
     */
    ChatResponse createConversation(ChatRequest chatRequest);

    /**
     * 获取会话列表

     * @return
     */
    List<ChatResponse> getConversations();

    /**
     * 更新会话标题
     * @param conversationId
     * @param title
     */
    void updateConversationTitle(String conversationId, @NotBlank(message = "消息内容不能为空") String title);
}
