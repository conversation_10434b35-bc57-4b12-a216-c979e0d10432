package com.example.business.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.common.entity.dto.AiChatMessageDto;
import com.example.common.entity.vo.AiChatMessageVo;
import com.example.common.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.example.business.mapper.AiChatMessageMapper;
import com.example.business.entity.AiChatMessage;
import com.example.business.service.IAiChatMessageService;
import org.springframework.transaction.annotation.Transactional;

/**
 * AI聊天消息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AiChatMessageServiceImpl extends ServiceImpl<AiChatMessageMapper,AiChatMessage> implements IAiChatMessageService
{

    private final AiChatMessageMapper aiChatMessageMapper;

    /**
     * 查询AI聊天消息
     *
     * @param id AI聊天消息主键
     * @return AI聊天消息
     */
    @Override
    public AiChatMessage selectAiChatMessageById(Long id)
    {
        return aiChatMessageMapper.selectAiChatMessageById(id);
    }

    /**
     * 查询AI聊天消息列表
     *
     * @param aiChatMessageDto AI聊天消息
     * @return AI聊天消息
     */
    @Override
    public List<AiChatMessage> selectAiChatMessageList(AiChatMessageDto aiChatMessageDto)
    {
        return aiChatMessageMapper.selectAiChatMessageList(aiChatMessageDto);
    }

    /**
     * 新增AI聊天消息
     *
     * @param aiChatMessageDto AI聊天消息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertAiChatMessage(AiChatMessageDto aiChatMessageDto)
    {
        try {
            //持久化保存对话内容(AI)
            AiChatMessage aiChatMessageAi = AiChatMessage.builder()
                    .sessionId(aiChatMessageDto.getSessionId())
                    .rawContent(aiChatMessageDto.getRawContent())
                    .content(aiChatMessageDto.getRawContent())
                    .messageType("ai")
                    .responseTime(aiChatMessageDto.getResponseTime())
                    .build();


            //持久化保存对话内容(user)

            AiChatMessage aiChatMessageUser = AiChatMessage.builder()
                    .sessionId(aiChatMessageDto.getSessionId())
                    .content(aiChatMessageDto.getContent())
                    .rawContent(aiChatMessageDto.getContent())
                    .messageType("user")
                    .build();

            boolean userResult = this.save(aiChatMessageUser);
            boolean aiResult = this.save(aiChatMessageAi);

            if(userResult && aiResult){
                log.info("保存对话成功");
            }


        } catch (Exception e) {
            log.error("保存对话失败:{}",e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 修改AI聊天消息
     *
     * @param aiChatMessageDto AI聊天消息
     * @return 结果
     */
    @Override
    public int updateAiChatMessage(AiChatMessageDto aiChatMessageDto)
    {

        return aiChatMessageMapper.updateAiChatMessage(aiChatMessageDto);
    }

    /**
     * 批量删除AI聊天消息
     *
     * @param ids 需要删除的AI聊天消息主键
     * @return 结果
     */
    @Override
    public int deleteAiChatMessageByIds(Long[] ids)
    {
        return aiChatMessageMapper.deleteAiChatMessageByIds(ids);
    }

    /**
     * 删除AI聊天消息信息
     *
     * @param id AI聊天消息主键
     * @return 结果
     */
    @Override
    public int deleteAiChatMessageById(Long id)
    {
        return aiChatMessageMapper.deleteAiChatMessageById(id);
    }

    @Override
    public List<AiChatMessageVo> selectBySessionId(String sessionId) {
        List<AiChatMessageVo> aiChatMessageVos = aiChatMessageMapper.selectBySessionId(sessionId);
        log.info("对话详情列表获取成功，结果集为：{}",aiChatMessageVos.size());
        return aiChatMessageVos;
    }

    @Override
    public List<AiChatMessageVo> selectByAiInfo(String sessionId) {
        List<AiChatMessageVo> aiChatMessageVos = aiChatMessageMapper.selectByAiInfo(sessionId);
        System.err.println("1111111111111111111111");
        log.info("AI历史对话详情列表获取成功，结果集为：{}",aiChatMessageVos.size());
        return aiChatMessageVos;
    }


}
