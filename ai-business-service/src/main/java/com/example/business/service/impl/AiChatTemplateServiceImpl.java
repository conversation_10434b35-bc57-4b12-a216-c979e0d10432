package com.example.business.service.impl;

import java.util.Collections;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.common.entity.Enum.RedisCatchEnum;
import com.example.common.entity.vo.AiChatTemplateVo;
import com.example.common.utils.DateUtils;
import com.example.common.utils.GenericRedisUtil;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.example.business.mapper.AiChatTemplateMapper;
import com.example.business.entity.AiChatTemplate;
import com.example.business.service.IAiChatTemplateService;
import org.springframework.util.CollectionUtils;

/**
 * AI会话模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
public class AiChatTemplateServiceImpl extends ServiceImpl<AiChatTemplateMapper,AiChatTemplate> implements IAiChatTemplateService
{
    @Autowired
    private AiChatTemplateMapper aiChatTemplateMapper;
    @Resource
    private GenericRedisUtil genericRedisUtil;

    /**
     * 查询AI会话模板
     *
     * @param id AI会话模板主键
     * @return AI会话模板
     */
    @Override
    public AiChatTemplate selectAiChatTemplateById(Long id)
    {
        return aiChatTemplateMapper.selectAiChatTemplateById(id);
    }

    /**
     * 查询AI会话模板列表
     *
     * @param aiChatTemplate AI会话模板
     * @return AI会话模板
     */
    @Override
    public List<AiChatTemplate> selectAiChatTemplateList(AiChatTemplate aiChatTemplate)
    {
        return aiChatTemplateMapper.selectAiChatTemplateList(aiChatTemplate);
    }

    /**
     * 新增AI会话模板
     *
     * @param aiChatTemplate AI会话模板
     * @return 结果
     */
    @Override
    public int insertAiChatTemplate(AiChatTemplate aiChatTemplate)
    {
        aiChatTemplate.setCreateTime(DateUtils.getNowDate());
        return aiChatTemplateMapper.insertAiChatTemplate(aiChatTemplate);
    }

    /**
     * 修改AI会话模板
     *
     * @param aiChatTemplate AI会话模板
     * @return 结果
     */
    @Override
    public int updateAiChatTemplate(AiChatTemplate aiChatTemplate)
    {
        aiChatTemplate.setUpdateTime(DateUtils.getNowDate());
        return aiChatTemplateMapper.updateAiChatTemplate(aiChatTemplate);
    }

    /**
     * 批量删除AI会话模板
     *
     * @param ids 需要删除的AI会话模板主键
     * @return 结果
     */
    @Override
    public int deleteAiChatTemplateByIds(Long[] ids)
    {
        return aiChatTemplateMapper.deleteAiChatTemplateByIds(ids);
    }

    /**
     * 删除AI会话模板信息
     *
     * @param id AI会话模板主键
     * @return 结果
     */
    @Override
    public int deleteAiChatTemplateById(Long id)
    {
        return aiChatTemplateMapper.deleteAiChatTemplateById(id);
    }

    @Override
    public List<AiChatTemplateVo> getCacheList() {
        //获取缓存AI会话模板列表
        List<AiChatTemplateVo> aiChatTemplateVoList = genericRedisUtil.listGetAll(RedisCatchEnum.AI_CHAT_TEMPLATE.getTitle(), AiChatTemplateVo.class);
        if(CollectionUtils.isEmpty(aiChatTemplateVoList)){
            //缓存中不存在从数据库中获取
            List<AiChatTemplate> aiChatTemplates = aiChatTemplateMapper.selectList(null);
            if(CollectionUtils.isEmpty(aiChatTemplates)){
                log.warn("未找到AI会话模板数据，请先提添加数据");
                return List.of();
            }
            //将数据库中的数据存入缓存
            boolean success = genericRedisUtil.lSet(RedisCatchEnum.AI_CHAT_TEMPLATE.getTitle(), aiChatTemplates);
            if(success){
                aiChatTemplateVoList = genericRedisUtil.listGetAll(RedisCatchEnum.AI_CHAT_TEMPLATE.getTitle(), AiChatTemplateVo.class);
                return  aiChatTemplateVoList;
            }
            log.error("AI会话模板缓存失败");
        }
        return aiChatTemplateVoList;
    }
}
