package com.example.business.service.impl;

import com.example.common.aspect.OperationLogService;
import com.example.common.entity.dto.AiOperationLogDto;
import com.example.business.service.IAiOperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 操作日志服务实现
 * 用于异步保存操作日志，避免影响主业务流程
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@Service
public class OperationLogServiceImpl implements OperationLogService {

    @Autowired
    private IAiOperationLogService aiOperationLogService;

    /**
     * 异步保存操作日志
     * 使用异步方式避免日志记录影响主业务性能
     */
    @Override
    @Async("operationLogExecutor")  //单独开启线程执行
    public void saveLogAsync(AiOperationLogDto logDto) {
        try {
            aiOperationLogService.insertAiOperationLog(logDto);
            log.info("操作日志保存成功，traceId: {}, 操作类型: {}",
                logDto.getTraceId(), logDto.getOperationType());
        } catch (Exception e) {
            log.error("操作日志保存失败，traceId: {}, 操作类型: {}, 错误: {}", 
                logDto.getTraceId(), logDto.getOperationType(), e.getMessage(), e);
        }
    }

    /**
     * 同步保存操作日志
     * 用于需要确保日志记录成功的场景
     */
    @Override
    public void saveLogSync(AiOperationLogDto logDto) {
        try {
            aiOperationLogService.insertAiOperationLog(logDto);
            log.info("操作日志同步保存成功，traceId: {}, 操作类型: {}",
                logDto.getTraceId(), logDto.getOperationType());
        } catch (Exception e) {
            log.error("操作日志同步保存失败，traceId: {}, 操作类型: {}, 错误: {}", 
                logDto.getTraceId(), logDto.getOperationType(), e.getMessage(), e);
            throw e;
        }
    }
}
