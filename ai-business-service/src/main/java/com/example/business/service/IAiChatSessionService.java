package com.example.business.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.common.entity.dto.AiChatSessionDto;
import com.example.business.entity.AiChatSession;
import com.example.common.entity.vo.AiChatSessionVo;

/**
 * AI聊天会话Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IAiChatSessionService  extends IService<AiChatSession>
{
    /**
     * 查询AI聊天会话
     *
     * @param id AI聊天会话主键
     * @return AI聊天会话
     */
    public AiChatSession selectAiChatSessionById(Long id);

    /**
     * 查询AI聊天会话列表
     *
     * @param aiChatSession AI聊天会话
     * @return AI聊天会话集合
     */
    public List<AiChatSession> selectAiChatSessionList(AiChatSession aiChatSession);

    /**
     * 新增AI聊天会话
     *
     * @param aiChatSessionDto AI聊天会话
     * @return 结果
     */
    public AiChatSessionVo insertAiChatSession(AiChatSessionDto aiChatSessionDto);

    /**
     * 修改AI聊天会话
     *
     * @param aiChatSessionDto AI聊天会话
     * @return 结果
     */
    public int updateAiChatSession(AiChatSessionDto aiChatSessionDto);

    /**
     * 批量删除AI聊天会话
     *
     * @param ids 需要删除的AI聊天会话主键集合
     * @return 结果
     */
    public int deleteAiChatSessionByIds(Long[] ids);

    /**
     * 删除AI聊天会话信息
     *
     * @param id AI聊天会话主键
     * @return 结果
     */
    public int deleteAiChatSessionById(Long id);

    /**
     * 获取对话列表
     * @return
     */
    List<AiChatSessionVo> getConversations();
}
