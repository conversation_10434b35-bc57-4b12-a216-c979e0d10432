package com.example.business.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.example.business.mapper.AiConfigMapper;
import com.example.business.entity.AiConfig;
import com.example.business.service.IAiConfigService;

/**
 * AI配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
public class AiConfigServiceImpl extends ServiceImpl<AiConfigMapper,AiConfig> implements IAiConfigService
{
    @Autowired
    private AiConfigMapper aiConfigMapper;

    /**
     * 查询AI配置
     *
     * @param id AI配置主键
     * @return AI配置
     */
    @Override
    public AiConfig selectAiConfigById(Long id)
    {
        return aiConfigMapper.selectAiConfigById(id);
    }

    /**
     * 查询AI配置列表
     *
     * @param aiConfig AI配置
     * @return AI配置
     */
    @Override
    public List<AiConfig> selectAiConfigList(AiConfig aiConfig)
    {
        return aiConfigMapper.selectAiConfigList(aiConfig);
    }

    /**
     * 新增AI配置
     *
     * @param aiConfig AI配置
     * @return 结果
     */
    @Override
    public int insertAiConfig(AiConfig aiConfig)
    {
        aiConfig.setCreateTime(DateUtils.getNowDate());
        return aiConfigMapper.insertAiConfig(aiConfig);
    }

    /**
     * 修改AI配置
     *
     * @param aiConfig AI配置
     * @return 结果
     */
    @Override
    public int updateAiConfig(AiConfig aiConfig)
    {
        aiConfig.setUpdateTime(DateUtils.getNowDate());
        return aiConfigMapper.updateAiConfig(aiConfig);
    }

    /**
     * 批量删除AI配置
     *
     * @param ids 需要删除的AI配置主键
     * @return 结果
     */
    @Override
    public int deleteAiConfigByIds(Long[] ids)
    {
        return aiConfigMapper.deleteAiConfigByIds(ids);
    }

    /**
     * 删除AI配置信息
     *
     * @param id AI配置主键
     * @return 结果
     */
    @Override
    public int deleteAiConfigById(Long id)
    {
        return aiConfigMapper.deleteAiConfigById(id);
    }
}
