package com.example.business.service.impl;

import java.util.List;
import com.example.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.example.business.mapper.AiUserQuotaMapper;
import com.example.business.entity.AiUserQuota;
import com.example.business.service.IAiUserQuotaService;

/**
 * 用户AI使用配额Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
public class AiUserQuotaServiceImpl implements IAiUserQuotaService
{
    @Autowired
    private AiUserQuotaMapper aiUserQuotaMapper;

    /**
     * 查询用户AI使用配额
     *
     * @param id 用户AI使用配额主键
     * @return 用户AI使用配额
     */
    @Override
    public AiUserQuota selectAiUserQuotaById(Long id)
    {
        return aiUserQuotaMapper.selectAiUserQuotaById(id);
    }

    /**
     * 查询用户AI使用配额列表
     *
     * @param aiUserQuota 用户AI使用配额
     * @return 用户AI使用配额
     */
    @Override
    public List<AiUserQuota> selectAiUserQuotaList(AiUserQuota aiUserQuota)
    {
        return aiUserQuotaMapper.selectAiUserQuotaList(aiUserQuota);
    }

    /**
     * 新增用户AI使用配额
     *
     * @param aiUserQuota 用户AI使用配额
     * @return 结果
     */
    @Override
    public int insertAiUserQuota(AiUserQuota aiUserQuota)
    {
        aiUserQuota.setCreateTime(DateUtils.getNowDate());
        return aiUserQuotaMapper.insertAiUserQuota(aiUserQuota);
    }

    /**
     * 修改用户AI使用配额
     *
     * @param aiUserQuota 用户AI使用配额
     * @return 结果
     */
    @Override
    public int updateAiUserQuota(AiUserQuota aiUserQuota)
    {
        aiUserQuota.setUpdateTime(DateUtils.getNowDate());
        return aiUserQuotaMapper.updateAiUserQuota(aiUserQuota);
    }

    /**
     * 批量删除用户AI使用配额
     *
     * @param ids 需要删除的用户AI使用配额主键
     * @return 结果
     */
    @Override
    public int deleteAiUserQuotaByIds(Long[] ids)
    {
        return aiUserQuotaMapper.deleteAiUserQuotaByIds(ids);
    }

    /**
     * 删除用户AI使用配额信息
     *
     * @param id 用户AI使用配额主键
     * @return 结果
     */
    @Override
    public int deleteAiUserQuotaById(Long id)
    {
        return aiUserQuotaMapper.deleteAiUserQuotaById(id);
    }
}
