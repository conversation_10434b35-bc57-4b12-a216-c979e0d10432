package com.example.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.business.entity.AiChatSession;
import com.example.common.entity.vo.AiChatSessionVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

/**
 * AI聊天会话Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
@Repository
public interface AiChatSessionMapper extends BaseMapper<AiChatSession>
{
    /**
     * 查询AI聊天会话
     *
     * @param id AI聊天会话主键
     * @return AI聊天会话
     */
    public AiChatSession selectAiChatSessionById(Long id);

    /**
     * 查询AI聊天会话列表
     *
     * @param aiChatSession AI聊天会话
     * @return AI聊天会话集合
     */
    public List<AiChatSession> selectAiChatSessionList(AiChatSession aiChatSession);

    /**
     * 新增AI聊天会话
     *
     * @param aiChatSession AI聊天会话
     * @return 结果
     */
    public int insertAiChatSession(AiChatSession aiChatSession);


    /**
     * 删除AI聊天会话
     *
     * @param id AI聊天会话主键
     * @return 结果
     */
    public int deleteAiChatSessionById(Long id);

    /**
     * 批量删除AI聊天会话
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAiChatSessionByIds(Long[] ids);

    /**
     * 获取会话列表
     * @return
     */
    @Select("select session_id as 'id',title,create_time as 'createdAt',update_time as 'updatedAt' from ai_chat_session where deleted = 0 order by update_time desc ")
    List<AiChatSessionVo> getConversations();
}
