package com.example.business.mapper;

import java.util.List;
import com.example.business.entity.AiUserQuota;

/**
 * 用户AI使用配额Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface AiUserQuotaMapper
{
    /**
     * 查询用户AI使用配额
     *
     * @param id 用户AI使用配额主键
     * @return 用户AI使用配额
     */
    public AiUserQuota selectAiUserQuotaById(Long id);

    /**
     * 查询用户AI使用配额列表
     *
     * @param aiUserQuota 用户AI使用配额
     * @return 用户AI使用配额集合
     */
    public List<AiUserQuota> selectAiUserQuotaList(AiUserQuota aiUserQuota);

    /**
     * 新增用户AI使用配额
     *
     * @param aiUserQuota 用户AI使用配额
     * @return 结果
     */
    public int insertAiUserQuota(AiUserQuota aiUserQuota);

    /**
     * 修改用户AI使用配额
     *
     * @param aiUserQuota 用户AI使用配额
     * @return 结果
     */
    public int updateAiUserQuota(AiUserQuota aiUserQuota);

    /**
     * 删除用户AI使用配额
     *
     * @param id 用户AI使用配额主键
     * @return 结果
     */
    public int deleteAiUserQuotaById(Long id);

    /**
     * 批量删除用户AI使用配额
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAiUserQuotaByIds(Long[] ids);
}
