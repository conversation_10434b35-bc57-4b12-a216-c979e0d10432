package com.example.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.business.entity.AiChatMessage;
import com.example.common.entity.dto.AiChatMessageDto;
import com.example.common.entity.vo.AiChatMessageVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * AI聊天消息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
@Repository
public interface AiChatMessageMapper extends BaseMapper<AiChatMessage>
{
    /**
     * 查询AI聊天消息
     *
     * @param id AI聊天消息主键
     * @return AI聊天消息
     */
    public AiChatMessage selectAiChatMessageById(Long id);

    /**
     * 查询AI聊天消息列表
     *
     * @param aiChatMessageDto AI聊天消息
     * @return AI聊天消息集合
     */
    public List<AiChatMessage> selectAiChatMessageList(AiChatMessageDto aiChatMessageDto);

    /**
     * 新增AI聊天消息
     *
     * @param aiChatMessageDto AI聊天消息
     * @return 结果
     */
    public int insertAiChatMessage(AiChatMessageDto aiChatMessageDto);

    /**
     * 修改AI聊天消息
     *
     * @param aiChatMessageDto AI聊天消息
     * @return 结果
     */
    public int updateAiChatMessage(AiChatMessageDto aiChatMessageDto);

    /**
     * 删除AI聊天消息
     *
     * @param id AI聊天消息主键
     * @return 结果
     */
    public int deleteAiChatMessageById(Long id);

    /**
     * 批量删除AI聊天消息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAiChatMessageByIds(Long[] ids);


    /**
     * 根据会话id获取会话列表(只获取ai的回复)
     * @param sessionId
     * @return
     */
    @Select("SELECT * FROM ai_chat_message WHERE session_id = #{sessionId} and deleted = 0 and message_type = 'ai'")
    List<AiChatMessageVo> selectByAiInfo(String sessionId);

    /**
     * 根据会话id获取会话列表
     * @param sessionId
     * @return
     */
    @Select("SELECT session_id as 'id',message_type as 'type',response_time,raw_content as 'content' FROM ai_chat_message WHERE session_id = #{sessionId} and deleted = 0 ")
    List<AiChatMessageVo> selectBySessionId(String sessionId);
}
