package com.example.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.business.entity.AiOperationLog;

/**
 * AI系统操作日志Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface AiOperationLogMapper extends BaseMapper<AiOperationLog>
{
    /**
     * 查询AI系统操作日志
     *
     * @param id AI系统操作日志主键
     * @return AI系统操作日志
     */
    public AiOperationLog selectAiOperationLogById(Long id);

    /**
     * 查询AI系统操作日志列表
     *
     * @param aiOperationLog AI系统操作日志
     * @return AI系统操作日志集合
     */
    public List<AiOperationLog> selectAiOperationLogList(AiOperationLog aiOperationLog);

    /**
     * 新增AI系统操作日志
     *
     * @param aiOperationLog AI系统操作日志
     * @return 结果
     */
    public int insertAiOperationLog(AiOperationLog aiOperationLog);

    /**
     * 修改AI系统操作日志
     *
     * @param aiOperationLog AI系统操作日志
     * @return 结果
     */
    public int updateAiOperationLog(AiOperationLog aiOperationLog);

    /**
     * 删除AI系统操作日志
     *
     * @param id AI系统操作日志主键
     * @return 结果
     */
    public int deleteAiOperationLogById(Long id);

    /**
     * 批量删除AI系统操作日志
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAiOperationLogByIds(Long[] ids);
}
