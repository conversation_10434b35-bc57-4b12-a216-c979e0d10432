package com.example.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.example.business.entity.dto.ChatResponse;
import com.example.business.entity.Chat;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface ChatMapper extends BaseMapper<Chat> {
    @Select("SELECT chat_id as id,create_time as createdAt,update_time as updatedAt,chat_title as title" +
            " FROM chat where del_status = 1 order by update_time desc")
    List<ChatResponse> getConversations();

//    @Update("UPDATE chat SET chat_title = #{title},update_time = now() WHERE chat_id = #{conversationId}")
//    void updateConversationTitle(@Param("conversationId") String conversationId, @Param("title") String title);
}
