package com.example.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.business.entity.AiChatTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * AI会话模板Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
@Repository
public interface AiChatTemplateMapper extends BaseMapper<AiChatTemplate>
{
    /**
     * 查询AI会话模板
     *
     * @param id AI会话模板主键
     * @return AI会话模板
     */
    public AiChatTemplate selectAiChatTemplateById(Long id);

    /**
     * 查询AI会话模板列表
     *
     * @param aiChatTemplate AI会话模板
     * @return AI会话模板集合
     */
    public List<AiChatTemplate> selectAiChatTemplateList(AiChatTemplate aiChatTemplate);

    /**
     * 新增AI会话模板
     *
     * @param aiChatTemplate AI会话模板
     * @return 结果
     */
    public int insertAiChatTemplate(AiChatTemplate aiChatTemplate);

    /**
     * 修改AI会话模板
     *
     * @param aiChatTemplate AI会话模板
     * @return 结果
     */
    public int updateAiChatTemplate(AiChatTemplate aiChatTemplate);

    /**
     * 删除AI会话模板
     *
     * @param id AI会话模板主键
     * @return 结果
     */
    public int deleteAiChatTemplateById(Long id);

    /**
     * 批量删除AI会话模板
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAiChatTemplateByIds(Long[] ids);
}
