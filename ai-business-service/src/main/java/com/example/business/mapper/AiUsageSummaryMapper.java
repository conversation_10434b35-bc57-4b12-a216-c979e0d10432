package com.example.business.mapper;

import java.util.List;
import com.example.business.entity.AiUsageSummary;

/**
 * AI使用汇总统计Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface AiUsageSummaryMapper
{
    /**
     * 查询AI使用汇总统计
     *
     * @param id AI使用汇总统计主键
     * @return AI使用汇总统计
     */
    public AiUsageSummary selectAiUsageSummaryById(Long id);

    /**
     * 查询AI使用汇总统计列表
     *
     * @param aiUsageSummary AI使用汇总统计
     * @return AI使用汇总统计集合
     */
    public List<AiUsageSummary> selectAiUsageSummaryList(AiUsageSummary aiUsageSummary);

    /**
     * 新增AI使用汇总统计
     *
     * @param aiUsageSummary AI使用汇总统计
     * @return 结果
     */
    public int insertAiUsageSummary(AiUsageSummary aiUsageSummary);

    /**
     * 修改AI使用汇总统计
     *
     * @param aiUsageSummary AI使用汇总统计
     * @return 结果
     */
    public int updateAiUsageSummary(AiUsageSummary aiUsageSummary);

    /**
     * 删除AI使用汇总统计
     *
     * @param id AI使用汇总统计主键
     * @return 结果
     */
    public int deleteAiUsageSummaryById(Long id);

    /**
     * 批量删除AI使用汇总统计
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAiUsageSummaryByIds(Long[] ids);
}
