package com.example.business.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.example.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * AI聊天会话对象 ai_chat_session
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain=true)
public class AiChatSession extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 会话ID */

    private String sessionId;

    /** 用户ID */

    private Long userId;

    /** 会话标题 */

    private String title;

    /** 使用的AI模型 */

    private String model;

    /** 会话状态：1-活跃，2-已结束，3-已删除 */

    private Long status;

    /** 消息总数 */

    private Long messageCount;

    /** 总token消耗 */

    private Long totalTokens;

    /** 最后一条消息时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")

    private Date lastMessageTime;

    /** 会话配置 */

    private String config;

    /** 删除标志：0-未删除，1-已删除 */

    private Long deleted;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
