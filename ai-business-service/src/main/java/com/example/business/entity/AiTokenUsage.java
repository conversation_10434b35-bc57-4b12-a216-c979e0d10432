package com.example.business.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;


import com.example.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * AI Token使用统计对象 ai_token_usage
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain=true)
public class AiTokenUsage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 用户ID */

    private Long userId;

    /** 会话ID */

    private String sessionId;

    /** 消息ID */

    private String messageId;

    /** AI模型名称 */

    private String model;

    /** 输入Token数量 */

    private Long promptTokens;

    /** 输出Token数量 */

    private Long completionTokens;

    /** 总Token数量 */

    private Long totalTokens;

    /** 费用（美元） */

    private BigDecimal cost;

    /** 请求类型：chat-对话，completion-补全，embedding-嵌入 */

    private String requestType;

    /** 响应时间（毫秒） */

    private Long responseTime;

    /** 状态：1-成功，2-失败，3-超时 */

    private Long status;

    /** 错误代码 */

    private String errorCode;

    /** 错误信息 */

    private String errorMessage;

    /** 使用日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")

    private Date usageDate;

    /** 使用小时（0-23） */

    private Long usageHour;


    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
