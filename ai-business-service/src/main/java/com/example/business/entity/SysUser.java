package com.example.business.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.example.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 用户信息对象 sys_user
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain=true)
public class SysUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    private Long userId;

    /** 部门ID */

    private Long deptId;

    /** 用户账号 */

    private String userName;

    /** 用户昵称 */

    private String nickName;

    /** 用户类型（00系统用户） */

    private String userType;

    /** 用户邮箱 */

    private String email;

    /** 手机号码 */

    private String phonenumber;

    /** 用户性别（0男 1女 2未知） */

    private String sex;

    /** 头像地址 */

    private String avatar;

    /** 密码 */

    private String password;

    /** 账号状态（0正常 1停用） */

    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 最后登录IP */

    private String loginIp;

    /** 最后登录时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")

    private Date loginDate;

    /** 密码最后更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")

    private Date pwdUpdateDate;


    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
