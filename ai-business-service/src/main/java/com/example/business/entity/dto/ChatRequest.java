package com.example.business.entity.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 聊天请求DTO
 */
@Data
public class ChatRequest {

    @NotBlank(message = "消息内容不能为空")
    private String title;

    @NotNull(message = "用户ID不能为空")
    private Long userId;

    private String model = "openai"; // 默认使用OpenAI模型

    private String sessionId; // 会话ID，用于上下文管理

    private Double temperature = 0.7; // 温度参数

    private Integer maxTokens = 1000; // 最大token数
}
