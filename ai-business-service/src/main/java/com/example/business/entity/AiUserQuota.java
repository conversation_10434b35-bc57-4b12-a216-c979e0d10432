package com.example.business.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.example.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 用户AI使用配额对象 ai_user_quota
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain=true)
public class AiUserQuota extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 用户ID */

    private Long userId;

    /** 配额类型：daily-日配额，monthly-月配额，yearly-年配额，total-总配额 */

    private String quotaType;

    /** AI模型（NULL表示所有模型） */

    private String model;

    /** 最大Token数量 */

    private Long maxTokens;

    /** 最大请求次数 */

    private Long maxRequests;

    /** 最大费用（美元） */

    private BigDecimal maxCost;

    /** 已使用Token数量 */

    private Long usedTokens;

    /** 已使用请求次数 */

    private Long usedRequests;

    /** 已使用费用 */

    private BigDecimal usedCost;

    /** 重置日期（日配额每日重置，月配额每月重置） */

    private Date resetDate;

    /** 状态：1-正常，2-超限，3-禁用 */

    private Long status;

    /** 警告阈值（0.8表示80%时警告） */

    private BigDecimal warningThreshold;


    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
