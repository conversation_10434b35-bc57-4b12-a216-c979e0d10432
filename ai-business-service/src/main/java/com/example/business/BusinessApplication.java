package com.example.business;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * Hello world!
 *
 */
@SpringBootApplication(scanBasePackages = {"com.example.business", "com.example.common"})
@MapperScan("com.example.business.mapper")
@EnableDiscoveryClient
@EnableFeignClients
public class BusinessApplication
{
    public static void main(String[] args) {

        //解决bootstrap.yml 文件找不到问题 boot版本高于2.4
        System.setProperty("spring.cloud.bootstrap.enabled", "true");
        SpringApplication.run(BusinessApplication.class, args);
        System.err.println("business ai服务启动成功");

    }
}
