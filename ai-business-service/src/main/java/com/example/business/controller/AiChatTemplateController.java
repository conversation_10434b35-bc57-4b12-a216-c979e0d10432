package com.example.business.controller;

import com.example.business.service.IAiChatTemplateService;
import com.example.common.entity.vo.AiChatTemplateVo;
import com.example.common.result.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/ai/chatTemplate")
@RequiredArgsConstructor
public class AiChatTemplateController {

    private final IAiChatTemplateService iAiChatTemplateService;

    /**
     * 获取缓存AI会话模板列表
     * @return
     */
    @GetMapping("/getCacheList")
    public Result getCacheList() {
        List<AiChatTemplateVo> aiChatTemplateVoList =  iAiChatTemplateService.getCacheList();
        return Result.success(aiChatTemplateVoList);
    }
}
