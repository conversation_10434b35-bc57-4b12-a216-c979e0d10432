package com.example.business.controller;

import com.example.business.service.IAiChatSessionService;
import com.example.common.annotation.OperationLog;
import com.example.common.entity.Enum.OperationLogEnum;
import com.example.common.entity.dto.AiChatSessionDto;
import com.example.common.entity.vo.AiChatSessionVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 操作日志测试控制器
 * 用于演示和测试AOP操作日志功能
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Slf4j
@RestController
@RequestMapping("/api/test/operation-log")
@RequiredArgsConstructor
public class OperationLogTestController {

    private final IAiChatSessionService aiChatSessionService;

    /**
     * 测试创建会话的操作日志记录
     */
    @PostMapping("/create-session")
    public AiChatSessionVo createSession(@RequestBody AiChatSessionDto sessionDto) {
        log.info("收到创建会话请求，标题: {}, 用户ID: {}", sessionDto.getTitle(), sessionDto.getUserId());

        // 调用服务方法，会自动记录操作日志
        AiChatSessionVo result = aiChatSessionService.insertAiChatSession(sessionDto);

        log.info("会话创建成功，会话ID: {}", result.getId());
        return result;
    }

    /**
     * 测试更新会话的操作日志记录
     */
    @PutMapping("/update-session")
    public String updateSession(@RequestBody AiChatSessionDto sessionDto) {
        log.info("收到更新会话请求，会话ID: {}, 标题: {}", sessionDto.getSessionId(), sessionDto.getTitle());

        // 调用服务方法，会自动记录操作日志
        int result = aiChatSessionService.updateAiChatSession(sessionDto);

        log.info("会话更新结果: {}", result);
        return result > 0 ? "更新成功" : "更新失败";
    }

    /**
     * 测试简单的操作日志记录
     */
    @GetMapping("/simple-test")
//    @OperationLog(
//        operationType = OperationLogEnum.operation_type_config_change,
//        operationDesc = "简单测试操作",
//        recordRequest = true,
//        recordResponse = true,
//        recordExecutionTime = true
//    )
    public String simpleTest(@RequestParam(required = false) String message) {
        log.info("执行简单测试，消息: {}", message);

        // 模拟一些处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        return "测试完成，消息: " + (message != null ? message : "无消息");
    }

    /**
     * 测试异常情况的操作日志记录
     */
    @GetMapping("/error-test")
//    @OperationLog(
//        operationType = OperationLogEnum.operation_type_model_switch,
//        operationDesc = "异常测试操作",
//        recordRequest = true,
//        recordResponse = false,
//        recordExecutionTime = true
//    )
    public String errorTest(@RequestParam(defaultValue = "false") boolean throwError) {
        log.info("执行异常测试，是否抛出异常: {}", throwError);

        if (throwError) {
            throw new RuntimeException("这是一个测试异常");
        }

        return "测试正常完成";
    }

    /**
     * 测试不记录请求参数的操作日志
     */
    @PostMapping("/no-request-log")
//    @OperationLog(
//        operationType = OperationLogEnum.operation_type_quota_update,
//        operationDesc = "不记录请求参数的测试",
//        recordRequest = false,
//        recordResponse = true,
//        recordExecutionTime = true
//    )
    public String noRequestLogTest(@RequestBody String sensitiveData) {
        log.info("执行敏感数据处理测试");

        // 处理敏感数据，但不会记录到操作日志中
        return "敏感数据处理完成，长度: " + sensitiveData.length();
    }
}
