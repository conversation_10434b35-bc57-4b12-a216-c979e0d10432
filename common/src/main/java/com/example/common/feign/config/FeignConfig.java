package com.example.common.feign.config;

import com.example.common.feign.interceptor.FeignRequestInterceptor;
import com.example.common.feign.decoder.FeignErrorDecoder;
import feign.Logger;
import feign.Request;
import feign.Retryer;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * OpenFeign统一配置类
 * 提供全局的Feign客户端配置，包括超时、重试、日志、拦截器等
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Configuration
public class FeignConfig {

    /**
     * Feign请求选项配置
     * 设置连接超时和读取超时时间
     */
    @Bean
    public Request.Options feignRequestOptions() {
        return new Request.Options(
            5000,  // 连接超时时间：5秒
            TimeUnit.MILLISECONDS,
            10000, // 读取超时时间：10秒
            TimeUnit.MILLISECONDS,
            true   // 跟随重定向
        );
    }

    /**
     * Feign重试配置
     * 配置重试策略：最大重试次数、重试间隔等
     */
    @Bean
    public Retryer feignRetryer() {
        // 重试间隔100ms，最大重试间隔1000ms，最大重试次数3次
        return new Retryer.Default(100, 1000, 3);
    }

    /**
     * Feign日志级别配置
     * 设置详细的日志记录级别，便于调试和监控
     */
    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.BASIC; // BASIC级别：记录请求方法、URL、响应状态码和执行时间
    }

    /**
     * Feign请求拦截器
     * 统一处理请求头、认证信息、链路追踪等
     */
    @Bean
    public FeignRequestInterceptor feignRequestInterceptor() {
        return new FeignRequestInterceptor();
    }

    /**
     * Feign错误解码器
     * 统一处理服务间调用的错误响应
     */
    @Bean
    public ErrorDecoder feignErrorDecoder() {
        return new FeignErrorDecoder();
    }
}
