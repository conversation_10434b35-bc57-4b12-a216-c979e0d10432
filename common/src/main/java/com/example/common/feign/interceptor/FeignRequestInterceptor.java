package com.example.common.feign.interceptor;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.UUID;

/**
 * Feign请求拦截器
 * 统一处理微服务间调用的请求头传递、链路追踪、认证信息等
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
public class FeignRequestInterceptor implements RequestInterceptor {

    /**
     * 需要传递的请求头列表
     */
    private static final String[] FORWARD_HEADERS = {
        "Authorization",     // JWT Token
        "X-User-Id",        // 用户ID
        "X-User-Name",      // 用户名
        "X-Trace-Id",       // 链路追踪ID
        "X-Request-Id",     // 请求ID
        "Content-Type",     // 内容类型
        "Accept"            // 接受类型
    };

    @Override
    public void apply(RequestTemplate template) {
        try {
            // 获取当前请求上下文
            ServletRequestAttributes attributes = 
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                
                // 传递指定的请求头
                forwardHeaders(template, request);
                
                // 添加链路追踪信息
                addTraceInfo(template, request);
                
                // 添加服务调用标识
                addServiceCallInfo(template);
                
                log.debug("Feign请求拦截器处理完成 - URL: {}, Headers: {}", 
                    template.url(), template.headers().keySet());
            } else {
                // 非Web请求上下文（如定时任务、异步任务等）
                addDefaultTraceInfo(template);
                addServiceCallInfo(template);
                
                log.debug("非Web上下文Feign请求 - URL: {}", template.url());
            }
            
        } catch (Exception e) {
            log.error("Feign请求拦截器处理异常: {}", e.getMessage(), e);
            // 确保即使拦截器出错也不影响正常调用
            addDefaultTraceInfo(template);
        }
    }

    /**
     * 传递指定的请求头
     */
    private void forwardHeaders(RequestTemplate template, HttpServletRequest request) {
        for (String headerName : FORWARD_HEADERS) {
            String headerValue = request.getHeader(headerName);
            if (headerValue != null && !headerValue.trim().isEmpty()) {
                template.header(headerName, headerValue);
                log.debug("传递请求头: {} = {}", headerName, 
                    headerName.equals("Authorization") ? "***" : headerValue);
            }
        }
    }

    /**
     * 添加链路追踪信息
     */
    private void addTraceInfo(RequestTemplate template, HttpServletRequest request) {
        // 获取或生成链路追踪ID
        String traceId = request.getHeader("X-Trace-Id");
        if (traceId == null || traceId.trim().isEmpty()) {
            traceId = generateTraceId();
        }
        template.header("X-Trace-Id", traceId);
        
        // 获取或生成请求ID
        String requestId = request.getHeader("X-Request-Id");
        if (requestId == null || requestId.trim().isEmpty()) {
            requestId = generateRequestId();
        }
        template.header("X-Request-Id", requestId);
        
        log.debug("添加链路追踪信息 - TraceId: {}, RequestId: {}", traceId, requestId);
    }

    /**
     * 添加默认链路追踪信息（非Web上下文）
     */
    private void addDefaultTraceInfo(RequestTemplate template) {
        String traceId = generateTraceId();
        String requestId = generateRequestId();
        
        template.header("X-Trace-Id", traceId);
        template.header("X-Request-Id", requestId);
        template.header("X-Source", "INTERNAL"); // 标识为内部调用
        
        log.debug("添加默认链路追踪信息 - TraceId: {}, RequestId: {}", traceId, requestId);
    }

    /**
     * 添加服务调用标识
     */
    private void addServiceCallInfo(RequestTemplate template) {
        template.header("X-Service-Call", "true");
        template.header("X-Call-Time", String.valueOf(System.currentTimeMillis()));
    }

    /**
     * 生成链路追踪ID
     */
    private String generateTraceId() {
        return "trace-" + UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId() {
        return "req-" + UUID.randomUUID().toString().replace("-", "");
    }
}
