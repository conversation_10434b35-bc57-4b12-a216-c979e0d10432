package com.example.common.feign.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * Feign调用监控器
 * 统计微服务间调用的成功率、响应时间、调用次数等指标
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Component
public class FeignCallMonitor {

    /**
     * 服务调用统计信息
     */
    private final ConcurrentHashMap<String, ServiceCallStats> serviceStats = new ConcurrentHashMap<>();

    /**
     * 记录服务调用开始
     */
    public CallContext startCall(String serviceName, String methodName) {
        String key = serviceName + "#" + methodName;
        ServiceCallStats stats = serviceStats.computeIfAbsent(key, k -> new ServiceCallStats());
        
        stats.totalCalls.increment();
        
        return new CallContext(serviceName, methodName, System.currentTimeMillis());
    }

    /**
     * 记录服务调用成功
     */
    public void recordSuccess(CallContext context) {
        if (context == null) return;
        
        long duration = System.currentTimeMillis() - context.startTime;
        String key = context.serviceName + "#" + context.methodName;
        
        ServiceCallStats stats = serviceStats.get(key);
        if (stats != null) {
            stats.successCalls.increment();
            stats.totalResponseTime.add(duration);
            stats.updateMaxResponseTime(duration);
            
            log.debug("服务调用成功 - {}#{}, 耗时: {}ms", 
                context.serviceName, context.methodName, duration);
        }
    }

    /**
     * 记录服务调用失败
     */
    public void recordFailure(CallContext context, Throwable cause) {
        if (context == null) return;
        
        long duration = System.currentTimeMillis() - context.startTime;
        String key = context.serviceName + "#" + context.methodName;
        
        ServiceCallStats stats = serviceStats.get(key);
        if (stats != null) {
            stats.failureCalls.increment();
            stats.totalResponseTime.add(duration);
            
            log.warn("服务调用失败 - {}#{}, 耗时: {}ms, 错误: {}", 
                context.serviceName, context.methodName, duration, 
                cause != null ? cause.getMessage() : "未知错误");
        }
    }

    /**
     * 获取服务调用统计信息
     */
    public ServiceCallStats getServiceStats(String serviceName, String methodName) {
        String key = serviceName + "#" + methodName;
        return serviceStats.get(key);
    }

    /**
     * 获取所有服务调用统计信息
     */
    public ConcurrentHashMap<String, ServiceCallStats> getAllStats() {
        return new ConcurrentHashMap<>(serviceStats);
    }

    /**
     * 清空统计信息
     */
    public void clearStats() {
        serviceStats.clear();
        log.info("Feign调用统计信息已清空");
    }

    /**
     * 打印统计报告
     */
    public void printStatsReport() {
        log.info("=== Feign调用统计报告 ===");
        
        serviceStats.forEach((key, stats) -> {
            long total = stats.totalCalls.sum();
            long success = stats.successCalls.sum();
            long failure = stats.failureCalls.sum();
            double successRate = total > 0 ? (double) success / total * 100 : 0;
            long avgResponseTime = total > 0 ? stats.totalResponseTime.sum() / total : 0;
            
            log.info("服务: {} | 总调用: {} | 成功: {} | 失败: {} | 成功率: {:.2f}% | 平均响应时间: {}ms | 最大响应时间: {}ms",
                key, total, success, failure, successRate, avgResponseTime, stats.maxResponseTime.get());
        });
        
        log.info("=== 统计报告结束 ===");
    }

    /**
     * 调用上下文
     */
    public static class CallContext {
        public final String serviceName;
        public final String methodName;
        public final long startTime;

        public CallContext(String serviceName, String methodName, long startTime) {
            this.serviceName = serviceName;
            this.methodName = methodName;
            this.startTime = startTime;
        }
    }

    /**
     * 服务调用统计信息
     */
    public static class ServiceCallStats {
        public final LongAdder totalCalls = new LongAdder();      // 总调用次数
        public final LongAdder successCalls = new LongAdder();    // 成功调用次数
        public final LongAdder failureCalls = new LongAdder();    // 失败调用次数
        public final LongAdder totalResponseTime = new LongAdder(); // 总响应时间
        public final AtomicLong maxResponseTime = new AtomicLong(0); // 最大响应时间

        /**
         * 更新最大响应时间
         */
        public void updateMaxResponseTime(long responseTime) {
            maxResponseTime.updateAndGet(current -> Math.max(current, responseTime));
        }

        /**
         * 获取成功率
         */
        public double getSuccessRate() {
            long total = totalCalls.sum();
            return total > 0 ? (double) successCalls.sum() / total * 100 : 0;
        }

        /**
         * 获取平均响应时间
         */
        public long getAverageResponseTime() {
            long total = totalCalls.sum();
            return total > 0 ? totalResponseTime.sum() / total : 0;
        }
    }
}
