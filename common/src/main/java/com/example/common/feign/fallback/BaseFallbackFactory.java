package com.example.common.feign.fallback;

import com.example.common.exception.RemoteServiceException;
import com.example.common.exception.UserFriendlyException;
import com.example.common.result.Result;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;

import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 基础降级工厂类
 * 提供通用的降级处理逻辑，各个服务的FallbackFactory可以继承此类
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
public abstract class BaseFallbackFactory<T> implements FallbackFactory<T> {

    /**
     * 服务名称
     */
    protected final String serviceName;

    public BaseFallbackFactory(String serviceName) {
        this.serviceName = serviceName;
    }

    /**
     * 提取异常的错误信息
     */
    protected String extractErrorMessage(Throwable cause) {
        if (cause == null) {
            return "未知错误";
        }

        // 处理不同类型的异常
        if (cause instanceof ConnectException) {
            return String.format("无法连接到%s服务", serviceName);
        } else if (cause instanceof SocketTimeoutException) {
            return String.format("%s服务响应超时", serviceName);
        } else if (cause instanceof FeignException) {
            FeignException feignException = (FeignException) cause;
            return extractFeignExceptionMessage(feignException);
        } else if (cause instanceof RemoteServiceException) {
            RemoteServiceException remoteException = (RemoteServiceException) cause;
            return remoteException.getOriginalMessage();
        } else if (cause instanceof UserFriendlyException) {
            UserFriendlyException userException = (UserFriendlyException) cause;
            return userException.getUserMessage();
        }

        // 默认错误信息
        String message = cause.getMessage();
        return message != null ? message : String.format("%s服务暂时不可用", serviceName);
    }

    /**
     * 提取FeignException的错误信息
     */
    private String extractFeignExceptionMessage(FeignException feignException) {
        String responseBody = feignException.contentUTF8();
        
        // 如果有响应体，尝试提取有用信息
        if (responseBody != null && !responseBody.trim().isEmpty()) {
            // 如果响应体不是JSON且长度合理，直接使用
            if (!responseBody.trim().startsWith("{") && responseBody.length() < 100) {
                return responseBody.trim();
            }
            
            // 对于JSON响应，返回通用错误信息
            return String.format("%s服务返回错误(状态码:%d)", serviceName, feignException.status());
        }
        
        // 根据状态码返回友好的错误信息
        switch (feignException.status()) {
            case 400:
                return "请求参数错误";
            case 401:
                return "认证失败，请重新登录";
            case 403:
                return "权限不足";
            case 404:
                return "请求的资源不存在";
            case 429:
                return "请求过于频繁，请稍后重试";
            case 500:
                return String.format("%s服务内部错误", serviceName);
            case 502:
            case 503:
            case 504:
                return String.format("%s服务暂时不可用", serviceName);
            default:
                return String.format("%s服务调用失败", serviceName);
        }
    }

    /**
     * 创建错误结果
     */
    protected <R> Result<R> createErrorResult(Throwable cause) {
        String errorMessage = extractErrorMessage(cause);
        
        // 根据异常类型确定错误码
        int errorCode = 503; // 默认服务不可用
        
        if (cause instanceof FeignException) {
            FeignException feignException = (FeignException) cause;
            errorCode = feignException.status();
        } else if (cause instanceof RemoteServiceException) {
            RemoteServiceException remoteException = (RemoteServiceException) cause;
            errorCode = remoteException.getCode() != null ? remoteException.getCode() : 503;
        }
        
        return Result.error(errorCode, errorMessage);
    }

    /**
     * 创建空列表结果（用于列表查询的降级）
     */
    protected <R> Result<List<R>> createEmptyListResult(Throwable cause) {
        String errorMessage = extractErrorMessage(cause);
        log.warn("{}服务列表查询降级，返回空列表: {}", serviceName, errorMessage);
        
        // 对于列表查询，返回空列表而不是错误，但在日志中记录问题
        return Result.success("数据获取异常，显示空列表", new ArrayList<>());
    }

    /**
     * 创建空列表（不包装在Result中）
     */
    protected <R> List<R> createEmptyList(Throwable cause) {
        String errorMessage = extractErrorMessage(cause);
        log.warn("{}服务列表查询降级，返回空列表: {}", serviceName, errorMessage);
        
        return Collections.emptyList();
    }

    /**
     * 记录降级日志
     */
    protected void logFallback(String methodName, Throwable cause) {
        String errorMessage = extractErrorMessage(cause);
        log.error("🔥 {}服务.{} 调用失败，执行降级逻辑: {}", 
            serviceName, methodName, errorMessage, cause);
    }

    /**
     * 记录降级日志（简化版本）
     */
    protected void logFallback(String methodName, String customMessage, Throwable cause) {
        log.error("🔥 {}服务.{} 调用失败，执行降级逻辑: {}", 
            serviceName, methodName, customMessage, cause);
    }
}
