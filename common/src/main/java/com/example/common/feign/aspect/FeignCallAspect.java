package com.example.common.feign.aspect;

import com.example.common.feign.monitor.FeignCallMonitor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * Feign调用监控切面
 * 自动监控所有Feign客户端的调用情况，记录成功率、响应时间等指标
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "feign.monitor.enabled", havingValue = "true", matchIfMissing = true)
public class FeignCallAspect {

    private final FeignCallMonitor feignCallMonitor;

    /**
     * 监控所有Feign客户端的方法调用
     * 切点：所有标注了@FeignClient的接口的方法
     */
    @Around("@within(org.springframework.cloud.openfeign.FeignClient)")
    public Object monitorFeignCall(ProceedingJoinPoint joinPoint) throws Throwable {
        // 提取服务名称和方法名称
        String serviceName = extractServiceName(joinPoint);
        String methodName = joinPoint.getSignature().getName();
        
        // 开始监控
        FeignCallMonitor.CallContext context = feignCallMonitor.startCall(serviceName, methodName);
        
        try {
            // 执行原方法
            Object result = joinPoint.proceed();
            
            // 记录成功调用
            feignCallMonitor.recordSuccess(context);
            
            log.debug("Feign调用成功 - {}#{}", serviceName, methodName);
            
            return result;
            
        } catch (Throwable throwable) {
            // 记录失败调用
            feignCallMonitor.recordFailure(context, throwable);
            
            log.warn("Feign调用失败 - {}#{}, 错误: {}", 
                serviceName, methodName, throwable.getMessage());
            
            // 重新抛出异常，让降级机制处理
            throw throwable;
        }
    }

    /**
     * 从切点中提取服务名称
     */
    private String extractServiceName(ProceedingJoinPoint joinPoint) {
        try {
            // 获取目标类的@FeignClient注解
            Class<?> targetClass = joinPoint.getTarget().getClass();
            
            // 如果是代理类，获取接口
            if (targetClass.getName().contains("$Proxy") || targetClass.getName().contains("CGLIB")) {
                Class<?>[] interfaces = targetClass.getInterfaces();
                if (interfaces.length > 0) {
                    targetClass = interfaces[0];
                }
            }
            
            // 查找@FeignClient注解
            org.springframework.cloud.openfeign.FeignClient feignClient = 
                targetClass.getAnnotation(org.springframework.cloud.openfeign.FeignClient.class);
            
            if (feignClient != null) {
                // 优先使用name属性，其次使用value属性
                String serviceName = feignClient.name();
                if (serviceName.isEmpty()) {
                    serviceName = feignClient.value();
                }
                if (!serviceName.isEmpty()) {
                    return serviceName;
                }
            }
            
            // 如果无法从注解获取，使用类名
            return targetClass.getSimpleName().replace("Feign", "").toLowerCase();
            
        } catch (Exception e) {
            log.warn("提取服务名称失败: {}", e.getMessage());
            return "unknown-service";
        }
    }
}
