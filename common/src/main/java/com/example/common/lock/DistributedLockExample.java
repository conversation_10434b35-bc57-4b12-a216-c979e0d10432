package com.example.common.lock;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 分布式锁使用示例
 * 
 * 演示了分布式锁在不同业务场景中的应用
 */
@Service
public class DistributedLockExample {

    private static final Logger logger = LoggerFactory.getLogger(DistributedLockExample.class);

    @Autowired
    private RedisDistributedLock distributedLock;

    /**
     * 示例1：防止定时任务重复执行
     * 
     * 场景：多个服务实例部署时，确保定时任务只在一个实例上执行
     */
    public void scheduledTaskExample() {
        String lockKey = "scheduled:task:daily-report";
        String lockValue = distributedLock.tryLock(lockKey, 300); // 5分钟过期
        
        if (lockValue != null) {
            try {
                logger.info("开始执行定时任务：生成日报");
                
                // 模拟任务执行
                generateDailyReport();
                
                logger.info("定时任务执行完成");
            } catch (Exception e) {
                logger.error("定时任务执行失败", e);
            } finally {
                // 确保释放锁
                distributedLock.unlock(lockKey, lockValue);
                logger.info("释放定时任务锁");
            }
        } else {
            logger.info("定时任务正在其他实例执行，跳过");
        }
    }

    /**
     * 示例2：库存扣减防超卖
     * 
     * 场景：电商系统中，多个用户同时购买商品时防止库存超卖
     */
    public boolean decreaseStock(Long productId, int quantity) {
        String lockKey = "stock:product:" + productId;
        String lockValue = distributedLock.tryLockWithRetry(lockKey, 10, 3, 100);
        
        if (lockValue != null) {
            try {
                logger.info("获取商品{}库存锁成功", productId);
                
                // 查询当前库存
                int currentStock = getCurrentStock(productId);
                
                if (currentStock >= quantity) {
                    // 扣减库存
                    updateStock(productId, currentStock - quantity);
                    logger.info("商品{}库存扣减成功，扣减数量：{}", productId, quantity);
                    return true;
                } else {
                    logger.warn("商品{}库存不足，当前库存：{}，需要：{}", 
                        productId, currentStock, quantity);
                    return false;
                }
                
            } catch (Exception e) {
                logger.error("库存扣减失败", e);
                return false;
            } finally {
                distributedLock.unlock(lockKey, lockValue);
                logger.info("释放商品{}库存锁", productId);
            }
        } else {
            logger.warn("获取商品{}库存锁失败，请稍后重试", productId);
            return false;
        }
    }

    /**
     * 示例3：缓存更新防击穿
     * 
     * 场景：热点数据缓存失效时，防止大量请求同时查询数据库
     */
    public String getCachedData(String cacheKey) {
        // 先尝试从缓存获取
        String cachedData = getFromCache(cacheKey);
        if (cachedData != null) {
            return cachedData;
        }
        
        // 缓存未命中，使用分布式锁防止缓存击穿
        String lockKey = "cache:rebuild:" + cacheKey;
        String lockValue = distributedLock.tryLock(lockKey, 30); // 30秒过期
        
        if (lockValue != null) {
            try {
                logger.info("获取缓存重建锁成功，开始重建缓存：{}", cacheKey);
                
                // 双重检查，可能其他线程已经重建了缓存
                cachedData = getFromCache(cacheKey);
                if (cachedData != null) {
                    return cachedData;
                }
                
                // 从数据库查询数据
                String dataFromDb = queryFromDatabase(cacheKey);
                
                // 更新缓存
                updateCache(cacheKey, dataFromDb);
                
                logger.info("缓存重建完成：{}", cacheKey);
                return dataFromDb;
                
            } catch (Exception e) {
                logger.error("缓存重建失败", e);
                // 降级处理：直接查询数据库
                return queryFromDatabase(cacheKey);
            } finally {
                distributedLock.unlock(lockKey, lockValue);
                logger.info("释放缓存重建锁：{}", cacheKey);
            }
        } else {
            logger.info("其他线程正在重建缓存，等待后重试：{}", cacheKey);
            
            // 等待一段时间后重试获取缓存
            try {
                Thread.sleep(100);
                cachedData = getFromCache(cacheKey);
                if (cachedData != null) {
                    return cachedData;
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            // 如果还是获取不到，直接查询数据库
            return queryFromDatabase(cacheKey);
        }
    }

    // 模拟方法
    private void generateDailyReport() {
        try {
            Thread.sleep(2000); // 模拟任务执行时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    private int getCurrentStock(Long productId) {
        // 模拟查询数据库获取库存
        return 100;
    }

    private void updateStock(Long productId, int newStock) {
        // 模拟更新数据库库存
        logger.info("更新商品{}库存为：{}", productId, newStock);
    }

    private String getFromCache(String key) {
        // 模拟从Redis缓存获取数据
        return null; // 假设缓存未命中
    }

    private String queryFromDatabase(String key) {
        // 模拟从数据库查询数据
        return "data_from_db_" + key;
    }

    private void updateCache(String key, String value) {
        // 模拟更新Redis缓存
        logger.info("更新缓存：{} = {}", key, value);
    }
}
