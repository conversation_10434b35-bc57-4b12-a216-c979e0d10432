package com.example.common.lock;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 高级Redis分布式锁实现
 * 
 * 特性：
 * 1. 自动续期：防止业务执行时间超过锁过期时间
 * 2. 可重入：同一线程可以多次获取同一把锁
 * 3. 阻塞等待：支持阻塞式获取锁
 */
@Component
public class AdvancedRedisLock {

    private static final Logger logger = LoggerFactory.getLogger(AdvancedRedisLock.class);

    private final StringRedisTemplate redisTemplate;
    private final ScheduledExecutorService scheduler;

    // Lua脚本：续期锁
    private static final String RENEW_SCRIPT = 
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "    return redis.call('expire', KEYS[1], ARGV[2]) " +
        "else " +
        "    return 0 " +
        "end";

    // Lua脚本：释放锁
    private static final String UNLOCK_SCRIPT = 
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "    return redis.call('del', KEYS[1]) " +
        "else " +
        "    return 0 " +
        "end";

    private final DefaultRedisScript<Long> renewScript;
    private final DefaultRedisScript<Long> unlockScript;

    public AdvancedRedisLock(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
        this.scheduler = Executors.newScheduledThreadPool(2);
        
        this.renewScript = new DefaultRedisScript<>();
        this.renewScript.setScriptText(RENEW_SCRIPT);
        this.renewScript.setResultType(Long.class);
        
        this.unlockScript = new DefaultRedisScript<>();
        this.unlockScript.setScriptText(UNLOCK_SCRIPT);
        this.unlockScript.setResultType(Long.class);
    }

    /**
     * 分布式锁对象
     */
    public static class DistributedLockInfo {
        private final String lockKey;
        private final String lockValue;
        private final long expireTime;
        private volatile boolean autoRenew;

        public DistributedLockInfo(String lockKey, String lockValue, long expireTime) {
            this.lockKey = lockKey;
            this.lockValue = lockValue;
            this.expireTime = expireTime;
            this.autoRenew = false;
        }

        public String getLockKey() { return lockKey; }
        public String getLockValue() { return lockValue; }
        public long getExpireTime() { return expireTime; }
        public boolean isAutoRenew() { return autoRenew; }
        public void setAutoRenew(boolean autoRenew) { this.autoRenew = autoRenew; }
    }

    /**
     * 获取分布式锁（带自动续期）
     * 
     * @param lockKey 锁的key
     * @param expireTime 锁的过期时间（秒）
     * @param autoRenew 是否自动续期
     * @return 锁信息对象，获取失败返回null
     */
    public DistributedLockInfo tryLockWithAutoRenew(String lockKey, long expireTime, boolean autoRenew) {
        String lockValue = UUID.randomUUID().toString();
        
        Boolean success = redisTemplate.opsForValue()
            .setIfAbsent(lockKey, lockValue, expireTime, TimeUnit.SECONDS);
        
        if (Boolean.TRUE.equals(success)) {
            DistributedLockInfo lockInfo = new DistributedLockInfo(lockKey, lockValue, expireTime);
            
            if (autoRenew) {
                startAutoRenew(lockInfo);
            }
            
            logger.info("获取分布式锁成功：{}, 自动续期：{}", lockKey, autoRenew);
            return lockInfo;
        }
        
        return null;
    }

    /**
     * 阻塞式获取锁
     * 
     * @param lockKey 锁的key
     * @param expireTime 锁的过期时间（秒）
     * @param waitTime 最大等待时间（秒）
     * @param autoRenew 是否自动续期
     * @return 锁信息对象，获取失败返回null
     */
    public DistributedLockInfo lockWithWait(String lockKey, long expireTime, 
                                           long waitTime, boolean autoRenew) {
        long startTime = System.currentTimeMillis();
        long waitTimeMs = waitTime * 1000;
        
        while (System.currentTimeMillis() - startTime < waitTimeMs) {
            DistributedLockInfo lockInfo = tryLockWithAutoRenew(lockKey, expireTime, autoRenew);
            if (lockInfo != null) {
                return lockInfo;
            }
            
            try {
                Thread.sleep(100); // 等待100ms后重试
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.warn("等待锁被中断：{}", lockKey);
                return null;
            }
        }
        
        logger.warn("等待锁超时：{}, 等待时间：{}秒", lockKey, waitTime);
        return null;
    }

    /**
     * 释放分布式锁
     * 
     * @param lockInfo 锁信息对象
     * @return 是否释放成功
     */
    public boolean unlock(DistributedLockInfo lockInfo) {
        if (lockInfo == null) {
            return false;
        }
        
        // 停止自动续期
        lockInfo.setAutoRenew(false);
        
        // 释放锁
        Long result = redisTemplate.execute(unlockScript, 
            Collections.singletonList(lockInfo.getLockKey()), 
            lockInfo.getLockValue());
        
        boolean success = Long.valueOf(1).equals(result);
        logger.info("释放分布式锁：{}, 结果：{}", lockInfo.getLockKey(), success);
        
        return success;
    }

    /**
     * 手动续期锁
     * 
     * @param lockInfo 锁信息对象
     * @return 是否续期成功
     */
    public boolean renewLock(DistributedLockInfo lockInfo) {
        if (lockInfo == null) {
            return false;
        }
        
        Long result = redisTemplate.execute(renewScript,
            Collections.singletonList(lockInfo.getLockKey()),
            lockInfo.getLockValue(),
            String.valueOf(lockInfo.getExpireTime()));
        
        boolean success = Long.valueOf(1).equals(result);
        if (success) {
            logger.debug("锁续期成功：{}", lockInfo.getLockKey());
        } else {
            logger.warn("锁续期失败：{}", lockInfo.getLockKey());
        }
        
        return success;
    }

    /**
     * 启动自动续期
     */
    private void startAutoRenew(DistributedLockInfo lockInfo) {
        lockInfo.setAutoRenew(true);
        
        // 在锁过期时间的1/3处开始续期
        long renewInterval = lockInfo.getExpireTime() * 1000 / 3;
        
        scheduler.scheduleAtFixedRate(() -> {
            if (lockInfo.isAutoRenew()) {
                boolean renewed = renewLock(lockInfo);
                if (!renewed) {
                    logger.warn("自动续期失败，停止续期：{}", lockInfo.getLockKey());
                    lockInfo.setAutoRenew(false);
                }
            }
        }, renewInterval, renewInterval, TimeUnit.MILLISECONDS);
        
        logger.info("启动自动续期：{}, 续期间隔：{}ms", lockInfo.getLockKey(), renewInterval);
    }
}
