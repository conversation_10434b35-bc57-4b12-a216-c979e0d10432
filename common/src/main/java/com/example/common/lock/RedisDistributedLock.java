package com.example.common.lock;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * Redis分布式锁实现
 * 
 * 核心原理：
 * 1. 使用Redis的SET命令的NX选项实现互斥
 * 2. 使用EX选项设置过期时间防止死锁
 * 3. 使用UUID作为锁的值，确保只有持锁者才能释放锁
 * 4. 使用Lua脚本保证释放锁操作的原子性
 */
@Component
public class RedisDistributedLock {

    private final StringRedisTemplate redisTemplate;
    
    // Lua脚本：释放锁时验证锁的持有者
    private static final String UNLOCK_SCRIPT = 
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "    return redis.call('del', KEYS[1]) " +
        "else " +
        "    return 0 " +
        "end";
    
    private final DefaultRedisScript<Long> unlockScript;

    public RedisDistributedLock(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
        this.unlockScript = new DefaultRedisScript<>();
        this.unlockScript.setScriptText(UNLOCK_SCRIPT);
        this.unlockScript.setResultType(Long.class);
    }

    /**
     * 尝试获取分布式锁
     * 
     * @param lockKey 锁的key
     * @param expireTime 锁的过期时间（秒）
     * @return 锁的标识符，获取失败返回null
     */
    public String tryLock(String lockKey, long expireTime) {
        // 生成唯一标识符，确保只有持锁者才能释放锁
        String lockValue = UUID.randomUUID().toString();
        
        // 使用SET命令的NX和EX选项原子性地设置锁
        Boolean success = redisTemplate.opsForValue()
            .setIfAbsent(lockKey, lockValue, expireTime, TimeUnit.SECONDS);
        
        return Boolean.TRUE.equals(success) ? lockValue : null;
    }

    /**
     * 释放分布式锁
     * 
     * @param lockKey 锁的key
     * @param lockValue 锁的标识符
     * @return 是否释放成功
     */
    public boolean unlock(String lockKey, String lockValue) {
        // 使用Lua脚本确保释放锁的原子性
        // 只有当锁的值匹配时才删除，防止误删其他线程的锁
        Long result = redisTemplate.execute(unlockScript, 
            Collections.singletonList(lockKey), lockValue);
        
        return Long.valueOf(1).equals(result);
    }

    /**
     * 带重试的获取锁
     * 
     * @param lockKey 锁的key
     * @param expireTime 锁的过期时间（秒）
     * @param retryTimes 重试次数
     * @param retryInterval 重试间隔（毫秒）
     * @return 锁的标识符，获取失败返回null
     */
    public String tryLockWithRetry(String lockKey, long expireTime, 
                                   int retryTimes, long retryInterval) {
        for (int i = 0; i <= retryTimes; i++) {
            String lockValue = tryLock(lockKey, expireTime);
            if (lockValue != null) {
                return lockValue;
            }
            
            if (i < retryTimes) {
                try {
                    Thread.sleep(retryInterval);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return null;
                }
            }
        }
        return null;
    }
}
