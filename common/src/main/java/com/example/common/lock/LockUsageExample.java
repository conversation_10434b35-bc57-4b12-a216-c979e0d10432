package com.example.common.lock;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 分布式锁使用示例和最佳实践
 */
@Service
public class LockUsageExample {

    private static final Logger logger = LoggerFactory.getLogger(LockUsageExample.class);

    @Autowired
    private AdvancedRedisLock advancedRedisLock;

    /**
     * 示例：长时间任务使用自动续期锁
     */
    public void longRunningTaskExample() {
        String lockKey = "long:task:data-migration";
        
        // 获取带自动续期的锁，初始过期时间30秒
        AdvancedRedisLock.DistributedLockInfo lockInfo = 
            advancedRedisLock.tryLockWithAutoRenew(lockKey, 30, true);
        
        if (lockInfo != null) {
            try {
                logger.info("开始执行长时间任务：数据迁移");
                
                // 模拟长时间任务（可能超过30秒）
                performDataMigration();
                
                logger.info("长时间任务执行完成");
            } catch (Exception e) {
                logger.error("长时间任务执行失败", e);
            } finally {
                // 释放锁（会自动停止续期）
                advancedRedisLock.unlock(lockInfo);
            }
        } else {
            logger.warn("获取长时间任务锁失败，任务可能正在其他节点执行");
        }
    }

    /**
     * 示例：阻塞式获取锁
     */
    public boolean processOrderWithWait(String orderId) {
        String lockKey = "order:process:" + orderId;
        
        // 阻塞等待最多10秒获取锁
        AdvancedRedisLock.DistributedLockInfo lockInfo = 
            advancedRedisLock.lockWithWait(lockKey, 60, 10, false);
        
        if (lockInfo != null) {
            try {
                logger.info("开始处理订单：{}", orderId);
                
                // 处理订单逻辑
                return processOrder(orderId);
                
            } catch (Exception e) {
                logger.error("处理订单失败：{}", orderId, e);
                return false;
            } finally {
                advancedRedisLock.unlock(lockInfo);
                logger.info("订单处理完成，释放锁：{}", orderId);
            }
        } else {
            logger.warn("获取订单处理锁超时：{}", orderId);
            return false;
        }
    }

    /**
     * 示例：手动续期锁
     */
    public void manualRenewExample() {
        String lockKey = "manual:renew:example";
        
        AdvancedRedisLock.DistributedLockInfo lockInfo = 
            advancedRedisLock.tryLockWithAutoRenew(lockKey, 10, false);
        
        if (lockInfo != null) {
            try {
                logger.info("获取锁成功，开始执行任务");
                
                for (int i = 0; i < 5; i++) {
                    // 执行部分任务
                    performPartialTask(i);
                    
                    // 手动续期锁
                    boolean renewed = advancedRedisLock.renewLock(lockInfo);
                    if (!renewed) {
                        logger.error("锁续期失败，可能被其他进程获取");
                        break;
                    }
                    
                    logger.info("任务进度：{}/5，锁续期成功", i + 1);
                }
                
            } catch (Exception e) {
                logger.error("任务执行失败", e);
            } finally {
                advancedRedisLock.unlock(lockInfo);
            }
        }
    }

    // 模拟方法
    private void performDataMigration() {
        try {
            // 模拟长时间任务（60秒）
            for (int i = 0; i < 12; i++) {
                Thread.sleep(5000);
                logger.info("数据迁移进度：{}%", (i + 1) * 100 / 12);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    private boolean processOrder(String orderId) {
        try {
            Thread.sleep(2000); // 模拟订单处理
            logger.info("订单{}处理成功", orderId);
            return true;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }

    private void performPartialTask(int step) {
        try {
            Thread.sleep(3000); // 模拟任务执行
            logger.info("执行任务步骤：{}", step);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}

/**
 * Redis分布式锁底层原理总结：
 * 
 * 1. 原子性保证：
 *    - SET key value NX EX seconds：原子性地设置key和过期时间
 *    - Lua脚本：确保多个Redis命令的原子性执行
 * 
 * 2. 互斥性实现：
 *    - NX选项：只有key不存在时才设置成功
 *    - 唯一标识符：使用UUID确保只有持锁者才能释放锁
 * 
 * 3. 防死锁机制：
 *    - 过期时间：防止持锁进程崩溃导致死锁
 *    - 自动续期：防止业务执行时间超过锁过期时间
 * 
 * 4. 安全性保证：
 *    - 释放锁验证：通过Lua脚本验证锁的持有者
 *    - 原子性释放：GET + DEL操作的原子性
 * 
 * 5. 性能优化：
 *    - 重试机制：避免频繁轮询
 *    - 合理的过期时间：平衡安全性和性能
 * 
 * 6. 高可用性：
 *    - Redis集群：使用Redis Cluster或哨兵模式
 *    - Redlock算法：多个Redis实例的分布式锁算法
 */
