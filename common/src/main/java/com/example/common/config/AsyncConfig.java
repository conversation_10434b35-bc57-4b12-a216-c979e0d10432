package com.example.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步配置类
 * 配置操作日志异步执行器
 * 
 * <AUTHOR>
 * @date 2025-07-19
 */
@Slf4j
@Configuration
@EnableAsync //用于启用异步方法执行功能。  被 @Async 标注的方法会在单独的线程中执行
public class AsyncConfig {

    /**
     * 操作日志异步执行器
     * 专门用于异步保存操作日志，避免影响主业务流程
     */
    @Bean("operationLogExecutor")
    public Executor operationLogExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(2);
        // 最大线程数
        executor.setMaxPoolSize(5);
        // 队列容量
        executor.setQueueCapacity(100);
        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(60);
        // 线程名称前缀
        executor.setThreadNamePrefix("operation-log-");
        
        // 拒绝策略：调用者运行策略，确保日志不丢失
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        //关闭等待时间
        executor.setAwaitTerminationSeconds(60);
        //初始化线程池
        executor.initialize();
        
        log.info("操作日志异步执行器初始化完成，核心线程数: {}, 最大线程数: {}, 队列容量: {}", 
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        
        return executor;
    }
}
