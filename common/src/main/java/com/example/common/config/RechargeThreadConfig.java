package com.example.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * Create by 2025/7/28 16:28
 * desc 客户端充值类异步线程配置类
 */
@Configuration
@EnableAsync
@Slf4j
public class RechargeThreadConfig {

    /**
     * 	* IO密级 ：2 * N  如数据库操作
     * * CPU密级：1 + N    计算密集型
     */
    //获取当前电脑（服务器）的核心线程数
    final static int N = Runtime.getRuntime().availableProcessors();


    @Bean("RechargeAsync")
    public ThreadPoolTaskExecutor threadPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 设置核心线程数
        executor.setCorePoolSize(2 * N);
        // 设置最大线程数
        executor.setMaxPoolSize(2 * N);
        // 设置队列容量
        executor.setQueueCapacity(1000);
        // 设置线程活跃时间（秒）
        executor.setKeepAliveSeconds(60);
        // 设置默认线程名称
        executor.setThreadNamePrefix("recharge-thread-");
        // 设置拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 初始化线程池
        executor.initialize();

        return executor;
    }
}
