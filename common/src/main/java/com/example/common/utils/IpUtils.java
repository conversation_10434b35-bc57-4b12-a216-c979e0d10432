package com.example.common.utils;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;


import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * IP地址工具类
 * 用于获取客户端真实IP地址
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
public class IpUtils {
    
    private static final String UNKNOWN = "unknown";
    private static final String LOCALHOST_IPV4 = "127.0.0.1";
    private static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";
    
    /**
     * 获取客户端IP地址
     * 支持通过代理、负载均衡等方式获取真实IP
     */
    public static String getIpAddr(HttpServletRequest request) {
        if (request == null) {
            return UNKNOWN;
        }
        
        String ip = null;
        
        try {
            // X-Forwarded-For：Squid 服务代理
            ip = request.getHeader("X-Forwarded-For");
            if (isValidIp(ip)) {
                // 多次反向代理后会有多个ip值，第一个ip才是真实ip
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }
            
            // Proxy-Client-IP：apache 服务代理
            ip = request.getHeader("Proxy-Client-IP");
            if (isValidIp(ip)) {
                return ip;
            }
            
            // WL-Proxy-Client-IP：weblogic 服务代理
            ip = request.getHeader("WL-Proxy-Client-IP");
            if (isValidIp(ip)) {
                return ip;
            }
            
            // HTTP_CLIENT_IP：有些代理服务器
            ip = request.getHeader("HTTP_CLIENT_IP");
            if (isValidIp(ip)) {
                return ip;
            }
            
            // HTTP_X_FORWARDED_FOR：有些代理服务器
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
            if (isValidIp(ip)) {
                return ip;
            }
            
            // X-Real-IP：nginx服务代理
            ip = request.getHeader("X-Real-IP");
            if (isValidIp(ip)) {
                return ip;
            }
            
            // 获取客户端ip地址
            ip = request.getRemoteAddr();
            
            if (LOCALHOST_IPV4.equals(ip) || LOCALHOST_IPV6.equals(ip)) {
                // 根据网卡取本机配置的IP
                try {
                    InetAddress inet = InetAddress.getLocalHost();
                    ip = inet.getHostAddress();
                } catch (UnknownHostException e) {
                    log.warn("获取本机IP地址失败", e);
                }
            }
            
        } catch (Exception e) {
            log.warn("获取IP地址失败", e);
        }
        
        return ip;
    }
    
    /**
     * 验证IP地址是否有效
     */
    private static boolean isValidIp(String ip) {
        return StringUtils.hasText(ip) && !UNKNOWN.equalsIgnoreCase(ip);
    }
}
