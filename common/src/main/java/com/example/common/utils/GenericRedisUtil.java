package com.example.common.utils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 泛型Redis工具类
 * 提供类型安全的Redis操作方法
 *
 * <AUTHOR> Assistant
 * @date 2025-07-18
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GenericRedisUtil {

    private final RedisTemplate<String, Object> redisTemplate;

    // =============================通用操作============================

    /**
     * 设置过期时间
     * @param key 键
     * @param time 时间(秒)
     * @return 是否成功
     */
    public boolean expire(String key, long time) {
        try {
            if (time > 0) {
                redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }
            return true;
        } catch (Exception e) {
            log.error("设置过期时间失败, key: {}, time: {}", key, time, e);
            return false;
        }
    }


    /**
     * 设置自增值并设置过期时间
     * @param key
     * @param timeout
     * @return
     */
    public Long setIncrement(final String key,final Long timeout){
        return redisTemplate.opsForValue().increment(key,timeout);
    }


    /**
     * 获取过期时间
     * @param key 键
     * @return 过期时间(秒) 返回0代表永久有效
     */
    public long getExpire(String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 判断key是否存在
     * @param key 键
     * @return true存在 false不存在
     */
    public boolean hasKey(String key) {
        try {
            return Boolean.TRUE.equals(redisTemplate.hasKey(key));
        } catch (Exception e) {
            log.error("判断key是否存在失败, key: {}", key, e);
            return false;
        }
    }

    /**
     * 删除缓存
     * @param keys 可以传一个值或多个
     */
    public void delete(String... keys) {
        if (keys != null && keys.length > 0) {
            if (keys.length == 1) {
                redisTemplate.delete(keys[0]);
            } else {
                redisTemplate.delete(Arrays.asList(keys));
            }
        }
    }

    // ============================String操作=============================

    /**
     * 获取缓存值（泛型版本）
     * @param key 键
     * @param clazz 返回值类型
     * @return 值
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String key, Class<T> clazz) {
        try {
            Object value = redisTemplate.opsForValue().get(key);
            if (value == null) {
                return null;
            }
            return (T) value;
        } catch (Exception e) {
            log.error("获取缓存值失败, key: {}, type: {}", key, clazz.getSimpleName(), e);
            return null;
        }
    }

    /**
     * 获取缓存值（Object版本，保持兼容性）
     * @param key 键
     * @return 值
     */
    public Object get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 设置缓存值
     * @param key 键
     * @param value 值
     * @return 是否成功
     */
    public <T> boolean set(String key, T value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            log.error("设置缓存值失败, key: {}, value: {}", key, value, e);
            return false;
        }
    }

    /**
     * 设置缓存值并指定过期时间
     * @param key 键
     * @param value 值
     * @param time 过期时间(秒)
     * @return 是否成功
     */
    public <T> boolean set(String key, T value, long time) {
        try {
            if (time > 0) {
                redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
            } else {
                redisTemplate.opsForValue().set(key, value);
            }
            return true;
        } catch (Exception e) {
            log.error("设置缓存值失败, key: {}, value: {}, time: {}", key, value, time, e);
            return false;
        }
    }

    /**
     * 递增
     * @param key 键
     * @param delta 增量
     * @return 递增后的值
     */
    public long increment(String key, long delta) {
        if (delta < 0) {
            throw new IllegalArgumentException("递增因子必须大于0");
        }
        return redisTemplate.opsForValue().increment(key, delta);
    }

    /**
     * 递减
     * @param key 键
     * @param delta 减量
     * @return 递减后的值
     */
    public long decrement(String key, long delta) {
        if (delta < 0) {
            throw new IllegalArgumentException("递减因子必须大于0");
        }
        return redisTemplate.opsForValue().increment(key, -delta);
    }

    // ============================List操作=============================

    /**
     * 获取List缓存内容（泛型版本）
     * @param key 键
     * @param start 开始位置
     * @param end 结束位置 0到-1代表所有值
     * @param clazz 元素类型
     * @return List集合
     */
    @SuppressWarnings("unchecked")
    public <T> List<T> listGet(String key, long start, long end, Class<T> clazz) {
        try {
            List<Object> objects = redisTemplate.opsForList().range(key, start, end);
            if (objects == null) {
                return new ArrayList<>();
            }
            return (List<T>) objects;
        } catch (Exception e) {
            log.error("获取List缓存失败, key: {}, start: {}, end: {}, type: {}",
                     key, start, end, clazz.getSimpleName(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取完整List（泛型版本）
     * @param key 键
     * @param clazz 元素类型
     * @return List集合
     */
    public <T> List<T> listGetAll(String key, Class<T> clazz) {
        return listGet(key, 0, -1, clazz);
    }

    /**
     * 获取List缓存内容（Object版本，保持兼容性）
     * @param key 键
     * @param start 开始位置
     * @param end 结束位置
     * @return List集合
     */
    public List<Object> listGet(String key, long start, long end) {
        try {
            List<Object> result = redisTemplate.opsForList().range(key, start, end);
            return result != null ? result : new ArrayList<>();
        } catch (Exception e) {
            log.error("获取List缓存失败, key: {}, start: {}, end: {}", key, start, end, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取List长度
     * @param key 键
     * @return 长度
     */
    public long listSize(String key) {
        try {
            Long size = redisTemplate.opsForList().size(key);
            return size != null ? size : 0;
        } catch (Exception e) {
            log.error("获取List长度失败, key: {}", key, e);
            return 0;
        }
    }

    /**
     * 通过索引获取List中的值（泛型版本）
     * @param key 键
     * @param index 索引
     * @param clazz 返回值类型
     * @return 值
     */
    @SuppressWarnings("unchecked")
    public <T> T listGetByIndex(String key, long index, Class<T> clazz) {
        try {
            Object value = redisTemplate.opsForList().index(key, index);
            if (value == null) {
                return null;
            }
            return (T) value;
        } catch (Exception e) {
            log.error("通过索引获取List值失败, key: {}, index: {}, type: {}",
                     key, index, clazz.getSimpleName(), e);
            return null;
        }
    }

    /**
     * 向List右侧添加单个元素
     * @param key 键
     * @param value 值
     * @return 是否成功
     */
    public <T> boolean listRightPush(String key, T value) {
        try {
            redisTemplate.opsForList().rightPush(key, value);
            return true;
        } catch (Exception e) {
            log.error("向List添加元素失败, key: {}, value: {}", key, value, e);
            return false;
        }
    }

    /**
     * 向List右侧添加多个元素
     * @param key 键
     * @param values 值列表
     * @return 是否成功
     */
    public <T> boolean listRightPushAll(String key, List<T> values) {
        try {
            if (CollectionUtils.isEmpty(values)) {
                return true;
            }
            redisTemplate.opsForList().rightPushAll(key, values.toArray());
            return true;
        } catch (Exception e) {
            log.error("向List批量添加元素失败, key: {}, size: {}", key, values.size(), e);
            return false;
        }
    }

    /**
     * 向List右侧添加元素并设置过期时间
     * @param key 键
     * @param value 值
     * @param time 过期时间(秒)
     * @return 是否成功
     */
    public <T> boolean listRightPush(String key, T value, long time) {
        try {
            redisTemplate.opsForList().rightPush(key, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("向List添加元素并设置过期时间失败, key: {}, value: {}, time: {}",
                     key, value, time, e);
            return false;
        }
    }

    /**
     * 向List右侧批量添加元素并设置过期时间
     * @param key 键
     * @param values 值列表
     * @param time 过期时间(秒)
     * @return 是否成功
     */
    public <T> boolean listRightPushAll(String key, List<T> values, long time) {
        try {
            if (CollectionUtils.isEmpty(values)) {
                return true;
            }
            redisTemplate.opsForList().rightPushAll(key, values.toArray());
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("向List批量添加元素并设置过期时间失败, key: {}, size: {}, time: {}",
                     key, values.size(), time, e);
            return false;
        }
    }

    /**
     * 根据索引修改List中的某条数据
     * @param key 键
     * @param index 索引
     * @param value 值
     * @return 是否成功
     */
    public <T> boolean listSet(String key, long index, T value) {
        try {
            redisTemplate.opsForList().set(key, index, value);
            return true;
        } catch (Exception e) {
            log.error("修改List元素失败, key: {}, index: {}, value: {}", key, index, value, e);
            return false;
        }
    }

    /**
     * 移除List中N个值为value的元素
     * @param key 键
     * @param count 移除数量
     * @param value 值
     * @return 移除的个数
     */
    public <T> long listRemove(String key, long count, T value) {
        try {
            Long removed = redisTemplate.opsForList().remove(key, count, value);
            return removed != null ? removed : 0;
        } catch (Exception e) {
            log.error("移除List元素失败, key: {}, count: {}, value: {}", key, count, value, e);
            return 0;
        }
    }

    // ============================Set操作=============================

    /**
     * 获取Set中的所有值（泛型版本）
     * @param key 键
     * @param clazz 元素类型
     * @return Set集合
     */
    @SuppressWarnings("unchecked")
    public <T> Set<T> setGetAll(String key, Class<T> clazz) {
        try {
            Set<Object> objects = redisTemplate.opsForSet().members(key);
            if (objects == null) {
                return new HashSet<>();
            }
            return (Set<T>) objects;
        } catch (Exception e) {
            log.error("获取Set所有元素失败, key: {}, type: {}", key, clazz.getSimpleName(), e);
            return new HashSet<>();
        }
    }

    /**
     * 判断Set中是否存在某个值
     * @param key 键
     * @param value 值
     * @return true存在 false不存在
     */
    public <T> boolean setHasValue(String key, T value) {
        try {
            return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(key, value));
        } catch (Exception e) {
            log.error("判断Set是否包含元素失败, key: {}, value: {}", key, value, e);
            return false;
        }
    }

    /**
     * 向Set中添加元素
     * @param key 键
     * @param values 值（可以是多个）
     * @return 成功添加的个数
     */
    @SafeVarargs
    public final <T> long setAdd(String key, T... values) {
        try {
            Long count = redisTemplate.opsForSet().add(key, (Object[]) values);
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("向Set添加元素失败, key: {}, values: {}", key, Arrays.toString(values), e);
            return 0;
        }
    }

    /**
     * 向Set中添加元素并设置过期时间
     * @param key 键
     * @param time 过期时间(秒)
     * @param values 值（可以是多个）
     * @return 成功添加的个数
     */
    @SafeVarargs
    public final <T> long setAdd(String key, long time, T... values) {
        try {
            Long count = redisTemplate.opsForSet().add(key, (Object[]) values);
            if (time > 0) {
                expire(key, time);
            }
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("向Set添加元素并设置过期时间失败, key: {}, time: {}, values: {}",
                     key, time, Arrays.toString(values), e);
            return 0;
        }
    }

    /**
     * 获取Set的长度
     * @param key 键
     * @return 长度
     */
    public long setSize(String key) {
        try {
            Long size = redisTemplate.opsForSet().size(key);
            return size != null ? size : 0;
        } catch (Exception e) {
            log.error("获取Set长度失败, key: {}", key, e);
            return 0;
        }
    }

    /**
     * 移除Set中的元素
     * @param key 键
     * @param values 值（可以是多个）
     * @return 移除的个数
     */
    @SafeVarargs
    public final <T> long setRemove(String key, T... values) {
        try {
            Long count = redisTemplate.opsForSet().remove(key, (Object[]) values);
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("移除Set元素失败, key: {}, values: {}", key, Arrays.toString(values), e);
            return 0;
        }
    }

    // ============================Hash操作=============================

    /**
     * 获取Hash中的值（泛型版本）
     * @param key 键
     * @param item 项
     * @param clazz 返回值类型
     * @return 值
     */
    @SuppressWarnings("unchecked")
    public <T> T hashGet(String key, String item, Class<T> clazz) {
        try {
            Object value = redisTemplate.opsForHash().get(key, item);
            if (value == null) {
                return null;
            }
            return (T) value;
        } catch (Exception e) {
            log.error("获取Hash值失败, key: {}, item: {}, type: {}",
                     key, item, clazz.getSimpleName(), e);
            return null;
        }
    }

    /**
     * 获取Hash中的值（Object版本，保持兼容性）
     * @param key 键
     * @param item 项
     * @return 值
     */
    public Object hashGet(String key, String item) {
        return redisTemplate.opsForHash().get(key, item);
    }

    /**
     * 获取Hash中的所有键值对
     * @param key 键
     * @return Map
     */
    public Map<Object, Object> hashGetAll(String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    /**
     * 设置Hash值
     * @param key 键
     * @param item 项
     * @param value 值
     * @return 是否成功
     */
    public <T> boolean hashSet(String key, String item, T value) {
        try {
            redisTemplate.opsForHash().put(key, item, value);
            return true;
        } catch (Exception e) {
            log.error("设置Hash值失败, key: {}, item: {}, value: {}", key, item, value, e);
            return false;
        }
    }

    /**
     * 设置Hash值并指定过期时间
     * @param key 键
     * @param item 项
     * @param value 值
     * @param time 过期时间(秒)
     * @return 是否成功
     */
    public <T> boolean hashSet(String key, String item, T value, long time) {
        try {
            redisTemplate.opsForHash().put(key, item, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("设置Hash值并指定过期时间失败, key: {}, item: {}, value: {}, time: {}",
                     key, item, value, time, e);
            return false;
        }
    }

    /**
     * 批量设置Hash值
     * @param key 键
     * @param map 键值对Map
     * @return 是否成功
     */
    public boolean hashSetAll(String key, Map<String, Object> map) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
            return true;
        } catch (Exception e) {
            log.error("批量设置Hash值失败, key: {}, map size: {}", key, map.size(), e);
            return false;
        }
    }

    /**
     * 批量设置Hash值并指定过期时间
     * @param key 键
     * @param map 键值对Map
     * @param time 过期时间(秒)
     * @return 是否成功
     */
    public boolean hashSetAll(String key, Map<String, Object> map, long time) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("批量设置Hash值并指定过期时间失败, key: {}, map size: {}, time: {}",
                     key, map.size(), time, e);
            return false;
        }
    }

    /**
     * 删除Hash中的值
     * @param key 键
     * @param items 项（可以是多个）
     */
    public void hashDelete(String key, Object... items) {
        redisTemplate.opsForHash().delete(key, items);
    }

    /**
     * 判断Hash中是否存在该项
     * @param key 键
     * @param item 项
     * @return true存在 false不存在
     */
    public boolean hashHasKey(String key, String item) {
        return redisTemplate.opsForHash().hasKey(key, item);
    }

    /**
     * Hash递增
     * @param key 键
     * @param item 项
     * @param delta 增量
     * @return 递增后的值
     */
    public double hashIncrement(String key, String item, double delta) {
        return redisTemplate.opsForHash().increment(key, item, delta);
    }

    /**
     * Hash递减
     * @param key 键
     * @param item 项
     * @param delta 减量
     * @return 递减后的值
     */
    public double hashDecrement(String key, String item, double delta) {
        return redisTemplate.opsForHash().increment(key, item, -delta);
    }

    // ============================便捷方法=============================

    /**
     * 兼容旧版本的lSet方法
     * @param key 键
     * @param value 值
     * @return 是否成功
     */
    public <T> boolean lSet(String key, T value) {
        return listRightPush(key, value);
    }

    /**
     * 兼容旧版本的lSet方法（带过期时间）
     * @param key 键
     * @param value 值
     * @param time 过期时间(秒)
     * @return 是否成功
     */
    public <T> boolean lSet(String key, T value, long time) {
        return listRightPush(key, value, time);
    }

    /**
     * 兼容旧版本的lSet方法（List版本）
     * @param key 键
     * @param values 值列表
     * @return 是否成功
     */
    public <T> boolean lSet(String key, List<T> values) {
        return listRightPushAll(key, values);
    }

    /**
     * 兼容旧版本的lSet方法（List版本带过期时间）
     * @param key 键
     * @param values 值列表
     * @param time 过期时间(秒)
     * @return 是否成功
     */
    public <T> boolean lSet(String key, List<T> values, long time) {
        return listRightPushAll(key, values, time);
    }
}
