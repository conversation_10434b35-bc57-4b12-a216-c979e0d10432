package com.example.common.entity.Enum;

/**
 * <AUTHOR>
 * Create by 2025/7/18 10:40
 * desc 缓存相关
 */

public enum RedisCatchEnum {
    AI_MODEL_CONFIG("ai_model_config"),
    AI_CHAT_TEMPLATE("ai_chat_template"),
    AI_CONFIG("ai_config");

    private String title;

    RedisCatchEnum(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
