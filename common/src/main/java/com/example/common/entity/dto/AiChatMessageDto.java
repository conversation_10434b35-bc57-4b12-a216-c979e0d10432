package com.example.common.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * Create by 2025/8/1 14:37
 * desc
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AiChatMessageDto {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 消息ID，唯一标识 */

    private String messageId;

    /** 会话ID */

    private String sessionId;

    /** 对话ID（兼容现有系统） */

    private String chatId;

    /** 用户ID */

    private Long userId;

    /** 消息类型：user-用户消息，ai-AI回复，system-系统消息 */

    private String messageType;

    /** 消息内容 */

    private String content;

    /** 原始内容（AI完整响应） */

    private String rawContent;

    /** 使用的AI模型 */

    private String model;

    /** 本次消息消耗的token数量 */

    private Long tokenUsage;

    /** 响应时间（毫秒） */

    private Long responseTime;

    /** 父消息ID（用于消息链） */

    private String parentMessageId;

    /** 消息在会话中的顺序 */

    private Long messageOrder;

    /** 消息状态：1-正常，2-已编辑，3-已删除，4-失败 */

    private Long status;

    /** 错误信息（如果有） */

    private String errorMessage;

    /** 元数据（JSON格式）包含额外信息 */

    private String metadata;

    /** 用户评分：1-5分 */

    private Long rating;

    /** 用户反馈 */

    private String feedback;

    /** 删除标志：0-未删除，1-已删除 */
}
