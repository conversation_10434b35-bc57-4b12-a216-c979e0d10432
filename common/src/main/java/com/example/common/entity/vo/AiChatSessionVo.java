package com.example.common.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * AI聊天会话VO对象
 * 返回前端的对象
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiChatSessionVo {

    /** 会话ID */
    private String id;

    /** 会话标题 */
    private String title;

    /** 用户ID */
    private Long userId;

    /** 使用的AI模型 */
    private String model;

    /** 会话ID */
    private String sessionId;

    /** 时间戳 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;

    /** token使用量 */
    private Integer tokenUsage;

    /** 消息ID */
    private String messageId;

    /** 创建时间 */
    private Date createdAt;

    /** 更新时间 */
    private Date updatedAt;
}
