package com.example.common.entity.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * VIP套餐VO
 * 用于返回给前端的数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class VipPlanVo {

    /**
     * 套餐ID
     */
    private String id;

    /**
     * 套餐名称
     */
    private String name;

    /**
     * 月付价格（分为单位）
     */
    private Long monthlyPrice;

    /**
     * 月付价格（元，格式化显示）
     */
    private String monthlyPriceFormatted;

    /**
     * 年付价格（分为单位）
     */
    private Long yearlyPrice;

    /**
     * 年付价格（元，格式化显示）
     */
    private String yearlyPriceFormatted;

    /**
     * 年付优惠金额（元）
     */
    private String yearlyDiscountFormatted;

    /**
     * 每日消息限制，-1表示无限制
     */
    private Integer dailyMessageLimit;

    /**
     * 每日消息限制显示文本
     */
    private String dailyMessageLimitText;

    /**
     * 历史消息保存天数，-1表示永久保存
     */
    private Integer historyDays;

    /**
     * 历史消息保存天数显示文本
     */
    private String historyDaysText;

    /**
     * 套餐描述
     */
    private String description;

    /**
     * 状态：0-下架，1-正常
     */
    private Integer status;

    /**
     * 状态文本
     */
    private String statusText;

    /**
     * 套餐特性列表
     */
    private List<VipPlanFeatureVo> features;

    /**
     * 可用模型列表
     */
    private List<VipPlanModelVo> models;

    /**
     * 是否推荐
     */
    private Boolean isRecommended;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
