package com.example.common.entity.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * AI聊天会话DTO对象
 * 接收前端的对象
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class AiChatSessionDto {

    /** 用户ID */
    private Long userId;

    /** 会话标题 */
    @NotBlank(message = "消息内容不能为空")
    private String title;

    /** 使用的AI模型 */
    private String model ;

    /**
     * 会话唯一id
     */
    private String sessionId;
}
