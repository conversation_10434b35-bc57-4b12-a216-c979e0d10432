package com.example.common.entity.Enum;

/**
 * <AUTHOR>
 * Create by 2025/7/18 10:40
 * desc 操作日志相关
 */

public enum OperationLogEnum {

    operation_type_chat("chat","用户发起聊天请求"),
    operation_type_quota_update("quota_update","系统检查用户配额"),
    operation_type_config_change("config_change","用户修改AI配置"),
    operation_type_model_switch("model_switch","模型切换操作"),
    user_agent_stystem("System","System");

    private String key;

    private String value;

    OperationLogEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
