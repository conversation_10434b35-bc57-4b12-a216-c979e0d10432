package com.example.common.annotation;

import com.example.common.entity.Enum.OperationLogEnum;

import java.lang.annotation.*;

/**
 * 操作日志注解
 * 用于标记需要记录操作日志的方法
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Target(ElementType.METHOD)  // 只能用在方法上
@Retention(RetentionPolicy.RUNTIME) // 运行时保留
@Documented
public @interface OperationLog {

    /**
     * 会话id
     * @return
     */
    String sessionId() default "";
    
    /**
     * 操作类型
     */
    OperationLogEnum operationType();
    
    /**
     * 操作描述（可选，如果不填则使用枚举中的描述）
     */
    String operationDesc() default "";
    
    /**
     * 是否记录请求参数
     */
    boolean recordRequest() default true;
    
    /**
     * 是否记录响应结果
     */
    boolean recordResponse() default true;
    
    /**
     * 是否记录执行时间
     */
    boolean recordExecutionTime() default true;
    
    /**
     * 用户ID的参数名（用于从方法参数中获取用户ID）
     * 如果为空，则尝试从请求上下文中获取
     */
    String userIdParam() default "";
    
    /**
     * 会话ID的参数名（用于从方法参数中获取会话ID）
     */
    String sessionIdParam() default "";
}
