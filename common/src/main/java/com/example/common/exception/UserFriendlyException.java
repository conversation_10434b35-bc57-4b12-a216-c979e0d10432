package com.example.common.exception;

/**
 * 用户友好异常类
 * 用于向用户展示友好的错误信息
 */
public class UserFriendlyException extends RuntimeException {
    private final String userMessage;
    private final String developerMessage;

    public UserFriendlyException(String userMessage, String developerMessage) {
        super(developerMessage);
        this.userMessage = userMessage;
        this.developerMessage = developerMessage;
    }

    public String getUserMessage() {
        return userMessage;
    }

    public String getDeveloperMessage() {
        return developerMessage;
    }
}
