package com.example.common.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 支付异常类
 * 专门用于处理支付相关的业务异常
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PaymentException extends RuntimeException {

    /**
     * 错误码
     */
    private Integer code;
    
    /**
     * 支付类型
     */
    private String paymentType;
    
    /**
     * 错误类型：UNSUPPORTED_TYPE, PAYMENT_FAILED, NETWORK_ERROR, etc.
     */
    private String errorType;

    public PaymentException(String message) {
        super(message);
        this.code = 500;
        this.errorType = "PAYMENT_ERROR";
    }

    public PaymentException(Integer code, String message) {
        super(message);
        this.code = code;
        this.errorType = "PAYMENT_ERROR";
    }

    public PaymentException(String paymentType, String message) {
        super(message);
        this.code = 500;
        this.paymentType = paymentType;
        this.errorType = "PAYMENT_ERROR";
    }

    public PaymentException(String paymentType, Integer code, String message, String errorType) {
        super(message);
        this.code = code;
        this.paymentType = paymentType;
        this.errorType = errorType;
    }

    public PaymentException(String message, Throwable cause) {
        super(message, cause);
        this.code = 500;
        this.errorType = "PAYMENT_ERROR";
    }

    public PaymentException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.errorType = "PAYMENT_ERROR";
    }

    /**
     * 创建不支持的支付类型异常
     */
    public static PaymentException unsupportedPaymentType(String paymentType) {
        return new PaymentException(paymentType, 400, 
            String.format("暂不支持 [%s] 支付方式，请选择其他支付方式", paymentType), 
            "UNSUPPORTED_TYPE");
    }

    /**
     * 创建支付失败异常
     */
    public static PaymentException paymentFailed(String paymentType, String reason) {
        return new PaymentException(paymentType, 500, 
            String.format("[%s] 支付失败: %s", paymentType, reason), 
            "PAYMENT_FAILED");
    }

    /**
     * 创建网络异常
     */
    public static PaymentException networkError(String paymentType, String reason) {
        return new PaymentException(paymentType, 503, 
            String.format("[%s] 支付服务暂时不可用: %s", paymentType, reason), 
            "NETWORK_ERROR");
    }
}
