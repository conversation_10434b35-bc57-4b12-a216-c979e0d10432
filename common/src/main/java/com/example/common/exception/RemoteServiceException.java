package com.example.common.exception;

/**
 * 远程服务调用异常
 * 用于封装远程服务调用时的异常信息
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public class RemoteServiceException extends RuntimeException {
    
    /**
     * 错误码
     */
    private final Integer code;
    
    /**
     * 服务名称
     */
    private final String serviceName;
    
    /**
     * 原始异常信息
     */
    private final String originalMessage;

    public RemoteServiceException(String serviceName, String message) {
        super(String.format("远程服务[%s]调用失败: %s", serviceName, message));
        this.code = 500;
        this.serviceName = serviceName;
        this.originalMessage = message;
    }

    public RemoteServiceException(String serviceName, Integer code, String message) {
        super(String.format("远程服务[%s]调用失败[%d]: %s", serviceName, code, message));
        this.code = code;
        this.serviceName = serviceName;
        this.originalMessage = message;
    }

    public RemoteServiceException(String serviceName, String message, Throwable cause) {
        super(String.format("远程服务[%s]调用失败: %s", serviceName, message), cause);
        this.code = 500;
        this.serviceName = serviceName;
        this.originalMessage = message;
    }

    public RemoteServiceException(String serviceName, Integer code, String message, Throwable cause) {
        super(String.format("远程服务[%s]调用失败[%d]: %s", serviceName, code, message), cause);
        this.code = code;
        this.serviceName = serviceName;
        this.originalMessage = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getServiceName() {
        return serviceName;
    }

    public String getOriginalMessage() {
        return originalMessage;
    }
}
