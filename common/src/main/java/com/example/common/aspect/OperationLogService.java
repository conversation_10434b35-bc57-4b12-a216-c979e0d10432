package com.example.common.aspect;

import com.example.common.entity.dto.AiOperationLogDto;
import org.springframework.scheduling.annotation.Async;

/**
 * 操作日志服务接口
 * 用于异步保存操作日志，避免影响主业务流程
 * 具体实现由各业务模块提供
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface OperationLogService {

    /**
     * 异步保存操作日志
     * 使用异步方式避免日志记录影响主业务性能
     */
    @Async("operationLogExecutor")
    void saveLogAsync(AiOperationLogDto logDto);

    /**
     * 同步保存操作日志
     * 用于需要确保日志记录成功的场景
     */
    void saveLogSync(AiOperationLogDto logDto);
}
