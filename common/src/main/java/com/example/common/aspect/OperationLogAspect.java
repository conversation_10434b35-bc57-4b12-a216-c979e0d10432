package com.example.common.aspect;

import com.alibaba.fastjson2.JSON;
import com.example.common.annotation.OperationLog;
import com.example.common.entity.dto.AiOperationLogDto;
import com.example.common.entity.vo.AiChatSessionVo;
import com.example.common.utils.IpUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.net.InetAddress;
import java.util.UUID;

/**
 * 操作日志切面
 * 自动记录标注了@OperationLog注解的方法的操作日志
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@Aspect //定义切面
@Component
public class OperationLogAspect {

    @Autowired(required = false)
    private OperationLogService operationLogService;

    @Around("@annotation(operationLog)") // 环绕通知：拦截所有标注了@OperationLog的方法
    public Object around(ProceedingJoinPoint joinPoint, OperationLog operationLog) throws Throwable {
        long startTime = System.currentTimeMillis();
        String traceId = UUID.randomUUID().toString().replace("-", "");
        
        // 获取方法信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object[] args = joinPoint.getArgs();
        
        // 构建操作日志对象
        AiOperationLogDto logDto = new AiOperationLogDto();
        
        try {
            // 设置基本信息
            logDto.setOperationType(operationLog.operationType().getKey())
                  .setOperationDesc(operationLog.operationDesc().isEmpty() ? 
                      operationLog.operationType().getValue() : operationLog.operationDesc())
                  .setTraceId(traceId);
            
            // 记录请求参数
            if (operationLog.recordRequest() && args.length > 0) {
                logDto.setRequestData(JSON.toJSONString(args));
            }
            
            // 获取用户ID和会话ID
            extractUserAndSessionId(logDto, operationLog, signature, args);
            
            // 获取IP地址和用户代理
            setRequestInfo(logDto);
            
            // 执行目标方法
            AiChatSessionVo result = (AiChatSessionVo) joinPoint.proceed();

            // 记录响应结果和记录会话id
            if (operationLog.recordResponse() && result != null) {
                logDto.setSessionId(result.getId());
                logDto.setResponseData(JSON.toJSONString(result));
            }
            
            // 记录执行时间
            if (operationLog.recordExecutionTime()) {
                long executionTime = System.currentTimeMillis() - startTime;
                logDto.setExecutionTime(executionTime);
            }
            
            // 设置成功状态
            logDto.setStatus(1L);
            
            // 异步保存日志
            saveOperationLog(logDto);
            
            return result;
            
        } catch (Throwable e) {
            // 记录异常信息
            long executionTime = System.currentTimeMillis() - startTime;
            logDto.setStatus(2L)
                  .setErrorCode(e.getClass().getSimpleName())
                  .setErrorMessage(e.getMessage())
                  .setExecutionTime(executionTime);
            
            // 异步保存日志
            saveOperationLog(logDto);
            
            log.error("操作日志记录异常，traceId: {}, 方法: {}, 错误: {}", 
                traceId, method.getName(), e.getMessage());
            
            throw e;
        }
    }

    /**
     * 保存操作日志
     * 如果有服务实现则使用服务，否则只记录到日志文件
     */
    private void saveOperationLog(AiOperationLogDto logDto) {
        if (operationLogService != null) {
            try {
                operationLogService.saveLogAsync(logDto);
            } catch (Exception e) {
                log.error("操作日志服务保存失败，traceId: {}, 错误: {}",
                    logDto.getTraceId(), e.getMessage());
                logToFile(logDto);
            }
        } else {
            // 如果没有服务实现，则记录到日志文件
            logToFile(logDto);
        }
    }

    /**
     * 记录日志到文件
     */
    private void logToFile(AiOperationLogDto logDto) {
        log.info("操作日志记录 - traceId: {}, 用户ID: {}, 操作类型: {}, 操作描述: {}, " +
                "执行时间: {}ms, 状态: {}, IP: {}",
            logDto.getTraceId(), logDto.getUserId(), logDto.getOperationType(),
            logDto.getOperationDesc(), logDto.getExecutionTime(),
            logDto.getStatus(), logDto.getIpAddress());
    }

    /**
     * 提取用户ID和会话ID
     */
    private void extractUserAndSessionId(AiOperationLogDto logDto, OperationLog operationLog, 
                                       MethodSignature signature, Object[] args) {
        Parameter[] parameters = signature.getMethod().getParameters();
        
        for (int i = 0; i < parameters.length; i++) {
            Parameter param = parameters[i];
            Object arg = args[i];
            
            if (arg == null) continue;
            
            // 获取用户ID
            if (!operationLog.userIdParam().isEmpty() && 
                param.getName().equals(operationLog.userIdParam())) {
                if (arg instanceof Long) {
                    logDto.setUserId((Long) arg);
                } else {
                    // 尝试从对象中获取userId字段
                    extractFieldValue(arg, "userId", Long.class, logDto::setUserId);
                }
            }
            
            // 获取会话ID
            if (!operationLog.sessionIdParam().isEmpty() && 
                param.getName().equals(operationLog.sessionIdParam())) {
                if (arg instanceof String) {
                    logDto.setSessionId((String) arg);
                } else {
                    // 尝试从对象中获取sessionId字段
                    extractFieldValue(arg, "sessionId", String.class, logDto::setSessionId);
                }
            }
            
            // 如果参数是DTO对象，尝试自动提取userId和sessionId
            if (operationLog.userIdParam().isEmpty() || operationLog.sessionIdParam().isEmpty()) {
                extractCommonFields(arg, logDto);
            }
        }
    }
    
    /**
     * 从对象中提取常用字段
     */
    private void extractCommonFields(Object obj, AiOperationLogDto logDto) {
        if (logDto.getUserId() == null) {
            extractFieldValue(obj, "userId", Long.class, logDto::setUserId);
        }
        if (logDto.getSessionId() == null) {
            extractFieldValue(obj, "sessionId", String.class, logDto::setSessionId);
            extractFieldValue(obj, "chatId", String.class, logDto::setSessionId);
        }
    }
    
    /**
     * 通过反射提取字段值
     */
    @SuppressWarnings("unchecked")
    private <T> void extractFieldValue(Object obj, String fieldName, Class<T> fieldType, 
                                     java.util.function.Consumer<T> setter) {
        try {
            java.lang.reflect.Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            Object value = field.get(obj);
            if (value != null && fieldType.isInstance(value)) {
                setter.accept((T) value);
            }
        } catch (Exception e) {
            // 忽略反射异常
        }
    }
    
    /**
     * 设置请求信息（IP地址、用户代理）
     */
    private void setRequestInfo(AiOperationLogDto logDto) {
        try {
            ServletRequestAttributes attributes = 
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                logDto.setIpAddress(IpUtils.getIpAddr(request))
                      .setUserAgent(request.getHeader("User-Agent"));
            } else {
                // 如果不是Web请求，使用本地IP
                InetAddress localHost = InetAddress.getLocalHost();
                logDto.setIpAddress(localHost.getHostAddress())
                      .setUserAgent("System");
            }
        } catch (Exception e) {
            log.warn("获取请求信息失败: {}", e.getMessage());
            logDto.setIpAddress("unknown").setUserAgent("System");
        }
    }
}
