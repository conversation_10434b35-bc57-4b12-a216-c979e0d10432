

# 服务器配置
server:
  port: 7002  # 支付服务端口
  servlet:
    context-path: # 应用上下文路径
    encoding:
      charset: UTF-8  # 字符编码
      enabled: true
      force: true
  # Tomcat配置
  tomcat:
    max-threads: 200  # 最大工作线程数
    min-spare-threads: 10  # 最小空闲线程数

##支付宝配置参数
alipay:
  appId: 2021000122637404
  ##支付宝沙盒私钥
  privateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCJbyImZ1vVoaW0OzdZkMsYAGgXsdXMsLzSASO5QRRDMkOk7IuNEDkP4083JIiBi/onv2P19+m0hk1scHp0e5EGv27T9mjfKQjAGKZDjZT/FdldFzp/LHF3DyDfcb7biy01FK7qLnA/9/XgbDNj2xpMAnWR7fY5GMCEi8lYA/0E6B/r0wZW3C4bW8H46sEPc8P2H4swBtms5zA120w9KmuoCmhBVdBPZygzFNe9Nomab2GW6NoMtP86SjqgfUDM5J5T6roGIb9Ez8gN6pYR4uohG9ZY5vgj2qw9b6mQ1M1U042o/gRYHrFkV6lJZ7soKMJKq3RoRvBbgTAaetgsmutDAgMBAAECggEACUM9OPtkXByd72zCST4SAhcc7OPmdfo/b6P2BWlWdUTw/C+ZJDPAtMpsL1o9/QUzIfSrRwuja7BVjYZzKmCdoKfMT2rfHJNGbLP8mLb1INAXK0gCgEm9h/m0DHs9EszpihdPxqbEl/sdTjfcqtE8j3zm8aPpIWYCq6xEMLNsHSVxmt8hAbrWVbj0hWVwksczLlAy/tYmK69msVbFxM8sYYvUyIjGFOV2ud6Jm8txAVx+eW06f3gpMZknYOP3OMPcxghtKZsI1m2BwblLFAM+AC9dLvvfrR9noDXAEpP7TYo+/DWt6oA7SdoEPPmFDXuQ+d4wnAl5aWo1G/TpGHzu8QKBgQD5Ce2Qy4cpT61oRrjIyY1Ont0c2/62JUQWqXklsaknVMOqaFpSok/6RSoYDWsxQi1MwtGPJhvbBAQPRlT6jBeKBUiTZQZ6NMk/8kmjkM6q+Brmo2/oKdm3j1Uhiydf4ZjqGIfQAUF0t+HaOSpPmUwerHLEr5YztNAGHbIArTTNewKBgQCNRpWof1gHXw0Y786EDgq+DFsaZzUdGyotgD5EtRBpb0H4TlCYafGWLSI+pzax19o6+Chz1ihu0Vlpp4LbKZzxbC4qc+NQkcX0tSgAKh+p5NL0U0f2XAF98lsyKuIeP9i3tQ13njOqF+V9DbQsnZryPeLD4K0s7Agg4OfYojra2QKBgQCl4FsAStBoT83s2s5Rzw30BBS7GD18DS5g+djbpo5IyEjbxE4FJF+pD1COXMG9OZr6ufjjJDsBkpcxpWMMzysOn55Lrsq1s1so4LTz4YwGjOM+JDZxqQ5/GVVW1l0PnDBCHLy5a1yx5ZjlxZXNFwqAk9SN9XZSUmkM6dKaaMP26QKBgER8RSyLnwbjwxullL1beUWsA7YG9i6H7qhkFMuF4tDZyGcwv81C/oRImOM6eD86XmZbynBtDui5v9iCBeNshtJaGP8ncriqEgaQkzWRNgxMCx+6B5ab/3UouaDWPQtg9BQKRt8H6A+6ydsXe5JtTKHF8ln2Xrz6Ju6ZWIsiCdnxAoGBAMszP5JpB2Ou/aIBE1z+GXy2HsM83dR0jmYguzSBqKmnHrFfb/NdaOYz6c6eq8FvgioR2KZssYAfbm0r9pNm6BX47OGj9+/pYAMwK51EYyKtkpEmc/rtnbJxGl80aJYGdOfW0eTleSPRsTNMz7xfb6KV+PbMeRMQqoFKHw6JMNhG
  ##支付宝沙盒公钥
  alipayPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5GbAsi8vdIylZTDX/OQpw/uh7MPbR5NBlnhCHoIvWOSWRGUVpYKw1+Bv8toRiOYgtGLz94fpfcF8R/F4wPW8vzKWkGx5cXsv6D9IU2ZDwbOR1VwBaKqsB74RTtpcKSgZ4JfMPyCy3qSURVf0Md2yUlJiKtoiQQgPhKb8OvoNH571vYq4+ciwTWgnzwxevBrIfO/j3K6eoERQGUt23HyG7hSf7zWSnYQ2bJ2zzf1Nzw9awekFu+Pn30cn1dwv63vH4AY14reGB9AdPguXypS/vXgDR3/1vTtwGzPhUsDYwi0iVwQniiPckP9flo7SZpHTC0meFDYh/MDSYhECkWr3CwIDAQAB
  ##支付宝网关地址（沙盒）
  url: https://openapi-sandbox.dl.alipaydev.com/gateway.do
  ##支付宝异步回调地址 （必须公网）
  notifyUrl: http://wechat.free.idcfengye.com/pay/alicallback
  ##支付宝同步跳转地址（前端，本地也可以）
  synchronousNotifyUrl:  http://localhost:81/Reservation/reservationRegister

