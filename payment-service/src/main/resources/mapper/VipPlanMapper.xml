<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.payment.mapper.VipPlanMapper">

    <resultMap id="AllVipPlansFeatures" type="VipPlanVo">
        <result property="id" column="id" />
        <result property="name" column="name" />
        <result property="monthlyPrice" column="monthly_price" />
        <result property="yearlyPrice" column="yearly_price" />
        <result property="dailyMessageLimit" column="daily_message_limit" />
        <result property="historyDays" column="history_days" />
        <result property="description" column="description" />
        <result property="status" column="status" />
        <result property="isRecommended" column="is_recommended" />
        <collection property="features" ofType="VipPlanFeatureVo">
            <result property="feature" column="feature" />
        </collection>
    </resultMap>
    <select id="getAllVipPlansFeatures" resultMap="AllVipPlansFeatures">
        SELECT vp.*,vpf.feature FROM `vip_plans` vp
        INNER JOIN vip_plan_features vpf on vp.id = vpf.plan_id
        order by vp.sort_order
    </select>
</mapper>
