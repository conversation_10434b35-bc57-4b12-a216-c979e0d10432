package com.example.payment.factory.impl;

import com.alibaba.fastjson2.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradePagePayModel;
import com.alipay.api.domain.AlipayTradePrecreateModel;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.api.request.AlipayTradePrecreateRequest;
import com.alipay.api.response.AlipayTradePagePayResponse;
import com.alipay.api.response.AlipayTradePrecreateResponse;
import com.example.payment.config.AliPayPramConfig;
import com.example.payment.factory.StrategyFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

/**
 * <AUTHOR>
 * Create by 2025/7/29 14:27
 * desc 支付宝业务类
 */
@Service
@Slf4j
@RequiredArgsConstructor  // 添加 Lombok 注解，自动生成构造函数进行依赖注入
public class AliPayImpl implements StrategyFactory {

    private final AliPayPramConfig aliPayPramConfig;  // 通过构造函数注入配置类

    @Override
    public String payStrategy() {
//
//        AlipayClient alipayClient = new DefaultAlipayClient(aliPayPramConfig.getUrl(),
//                aliPayPramConfig.getAppId(),
//                aliPayPramConfig.getPrivateKey(), "json", "utf-8",
//                aliPayPramConfig.getAlipayPublicKey(),
//                "RSA2");
//        AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();
//        //异步接收地址，仅支持http/https，公网可访问
////        request.setNotifyUrl(PayUtil.Alipay.notifyUrl);
//        //同步跳转地址，仅支持http/https (本地也可以)
////        request.setReturnUrl(PayUtil.Alipay.synchronousNotifyUrl);
//        /******必传参数******/
//        JSONObject bizContent = new JSONObject();
//        //商户订单号，商家自定义，保持唯一性
//        bizContent.put("out_trade_no", UUID.randomUUID().toString());
//        //支付金额，最小值0.01元
//        bizContent.put("total_amount", 0.01);
//        //订单标题，不可使用特殊符号
//        bizContent.put("subject", "测试支付宝支付");
//        //电脑网站支付场景固定传值FAST_INSTANT_TRADE_PAY
//        bizContent.put("product_code", "FAST_INSTANT_TRADE_PAY");
//
//        /******可选参数******/
//
//        /**
//         * 设置订单绝对超时时间，该笔订单允许的最晚付款时间，逾期将关闭交易。取值范围：1m～15d。m-分钟，h-小时，d-天，1c-当天（1c-当天的情况下，无论交易何时创建，都在0点关闭）。
//         * 该参数数值不接受小数点， 如 1.5h，可转换为 90m。
//         */
////        //获取当前时间戳
////        long time = new Date().getTime();
////        //延时时间= 当前时间戳+需要延时的时间（单位：毫秒）
////        long timeDelay = time + PayUtil.PayCountdownTime.delayedDate;
////        //将时间戳转换为日期格式
////        Date date = new Date(timeDelay);
////        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
////        String timeExpire = simpleDateFormat.format(date);
////        log.info("订单绝对超时时间-》{}",timeExpire);
////        bizContent.put("time_expire", timeExpire); //设置订单绝对超时时间 默认30分钟
//
//        request.setBizContent(bizContent.toString());
//        AlipayTradePagePayResponse response = null;
//        try {
//            // 如果需要返回GET请求，请使用
//            response = alipayClient.pageExecute(request, "GET");
//            //response = alipayClient.pageExecute(request, "POST");
//        } catch (AlipayApiException e) {
//            throw new RuntimeException(e);
//        }
//
//        String pageRedirectionData = response.getBody();
//        System.out.println(pageRedirectionData);
//
//        if (response.isSuccess()) {
//            System.out.println("调用成功");
//            log.info("支付宝拉起支付调用成功");
//            return pageRedirectionData;
//
//        } else {
//            System.out.println("调用失败");
//        }
        return this.payStrategyCode();
    }



    /**
     * 获取支付宝配置
     * @return AlipayConfig 支付宝配置对象
     */
    private AlipayConfig getAlipayConfig() {

        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl(aliPayPramConfig.getUrl());
        alipayConfig.setAppId(aliPayPramConfig.getAppId());
        alipayConfig.setPrivateKey(aliPayPramConfig.getPrivateKey());
        alipayConfig.setFormat("json");
        alipayConfig.setAlipayPublicKey(aliPayPramConfig.getAlipayPublicKey());
        alipayConfig.setCharset("UTF-8");
        alipayConfig.setSignType("RSA2");

        return alipayConfig;
    }


    public String payStrategyCode() {
        // 初始化SDK
        AlipayClient alipayClient = null;
        try {
            alipayClient = new DefaultAlipayClient(getAlipayConfig());
        } catch (AlipayApiException e) {
            throw new RuntimeException(e);
        }

        // 构造请求参数以调用接口
        AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();
        //异步接收地址，仅支持http/https，公网可访问
//        request.setNotifyUrl(PayUtil.Alipay.notifyUrl);
        //同步跳转地址，仅支持http/https (本地也可以)
//        request.setReturnUrl(PayUtil.Alipay.synchronousNotifyUrl);


        AlipayTradePagePayModel model = new AlipayTradePagePayModel();

        // 设置商户订单号
        model.setOutTradeNo("20150320010101001");

        // 设置订单总金额
        model.setTotalAmount("88.88");

        // 设置订单标题
        model.setSubject("Iphone6 16G");

        // 设置产品码
        model.setProductCode("FAST_INSTANT_TRADE_PAY");

        // 设置PC扫码支付的方式
        model.setQrPayMode("0");

        // 设置商户自定义二维码宽度
        model.setQrcodeWidth(100L);

        // 设置订单绝对超时时间
        model.setTimeExpire("2025-07-31 12:00:00");

        // 设置请求后页面的集成方式
        model.setIntegrationType("PCWEB");



        // 设置商户的原始订单号
        model.setMerchantOrderNo("20161008001");



        request.setBizModel(model);
        // 第三方代调用模式下请设置app_auth_token
        // request.putOtherTextParam("app_auth_token", "<-- 请填写应用授权令牌 -->");

//        AlipayTradePagePayResponse response = alipayClient.pageExecute(request, "POST");
        // 如果需要返回GET请求，请使用
        AlipayTradePagePayResponse response = null;
        try {
            response = alipayClient.pageExecute(request, "GET");
        } catch (AlipayApiException e) {
            throw new RuntimeException(e);
        }
        String pageRedirectionData = response.getBody();
        System.out.println(pageRedirectionData);

        if (response.isSuccess()) {
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
            // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
            // String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
            // System.out.println(diagnosisUrl);
        }
        return pageRedirectionData;
    }
}
