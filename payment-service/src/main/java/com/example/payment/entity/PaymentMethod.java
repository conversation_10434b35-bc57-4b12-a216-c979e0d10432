package com.example.payment.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.example.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 支付方式实体类
 * 对应数据库表：payment_methods
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("payment_methods")
public class PaymentMethod extends BaseEntity {

    /**
     * 支付方式ID
     */
    @TableId(type = IdType.INPUT)
    private String id;

    /**
     * 支付方式名称
     */
    private String name;

    /**
     * 支付方式描述
     */
    private String description;

    /**
     * 图标
     */
    private String icon;

    private String image;

    /**
     * 是否推荐：0-否，1-是
     */
    private Boolean isRecommended;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 排序顺序
     */
    private Integer sortOrder;
}
