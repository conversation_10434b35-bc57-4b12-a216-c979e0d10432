package com.example.payment.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 消费记录实体类
 * 对应数据库表：consumption_records
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@TableName("consumption_records")
public class ConsumptionRecord {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 关联交易ID
     */
    private String transactionId;

    /**
     * 消费类型：model_usage-模型使用，vip_purchase-购买VIP，other-其他
     */
    private String consumptionType;

    /**
     * 模型ID（如果是模型使用）
     */
    private String modelId;

    /**
     * 消费金额（分为单位）
     */
    private Long amount;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
