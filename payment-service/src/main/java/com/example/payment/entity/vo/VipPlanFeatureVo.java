package com.example.payment.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * VIP套餐特性VO
 * 用于返回给前端的数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class VipPlanFeatureVo {

    /**
     * ID
     */
    private Long id;

    /**
     * 套餐ID
     */
    private String planId;

    /**
     * 特性描述
     */
    private String feature;

    /**
     * 排序顺序
     */
    private Integer sortOrder;
}
