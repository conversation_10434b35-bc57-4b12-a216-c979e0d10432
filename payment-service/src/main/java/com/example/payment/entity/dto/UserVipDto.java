package com.example.payment.entity.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 用户VIP信息DTO
 * 用于接收前端请求数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class UserVipDto {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * VIP等级：free-免费用户，vip-VIP会员，super_vip-超级VIP
     */
    private String vipLevel;

    /**
     * VIP到期时间
     */
    private LocalDateTime expireTime;

    /**
     * 账户余额（分为单位）
     */
    @PositiveOrZero(message = "账户余额不能为负数")
    private Long balance;

    /**
     * 累计消费金额（分为单位）
     */
    @PositiveOrZero(message = "累计消费金额不能为负数")
    private Long totalSpent;
}
