package com.example.payment.entity.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * VIP套餐特性DTO
 * 用于接收前端请求数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class VipPlanFeatureDto {

    /**
     * 套餐ID
     */
    @NotBlank(message = "套餐ID不能为空")
    private String planId;

    /**
     * 特性描述
     */
    @NotBlank(message = "特性描述不能为空")
    private String feature;

    /**
     * 排序顺序
     */
    @NotNull(message = "排序顺序不能为空")
    private Integer sortOrder;
}
