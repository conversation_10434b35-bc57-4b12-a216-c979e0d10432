package com.example.payment.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 消费记录VO
 * 用于返回给前端的数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class ConsumptionRecordVo {

    /**
     * ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 关联交易ID
     */
    private String transactionId;

    /**
     * 消费类型：model_usage-模型使用，vip_purchase-购买VIP，other-其他
     */
    private String consumptionType;

    /**
     * 消费类型文本
     */
    private String consumptionTypeText;

    /**
     * 模型ID（如果是模型使用）
     */
    private String modelId;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 消费金额（分为单位）
     */
    private Long amount;

    /**
     * 消费金额（元，格式化显示）
     */
    private String amountFormatted;

    /**
     * 消费详情描述
     */
    private String consumptionDetail;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
