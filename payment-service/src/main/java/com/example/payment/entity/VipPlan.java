package com.example.payment.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.example.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * VIP套餐实体类
 * 对应数据库表：vip_plans
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("vip_plans")
public class VipPlan extends BaseEntity {

    /**
     * 套餐ID
     */
    @TableId(type = IdType.INPUT)
    private String id;

    /**
     * 套餐名称
     */
    private String name;

    /**
     * 月付价格（分为单位）
     */
    private Long monthlyPrice;

    /**
     * 年付价格（分为单位）
     */
    private Long yearlyPrice;

    /**
     * 每日消息限制，-1表示无限制
     */
    private Integer dailyMessageLimit;

    /**
     * 历史消息保存天数，-1表示永久保存
     */
    private Integer historyDays;

    /**
     * 套餐描述
     */
    private String description;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 是否推荐
     */
    private Boolean isRecommended;

    /**
     * 状态：0-下架，1-正常
     */
    private Integer status;
}
