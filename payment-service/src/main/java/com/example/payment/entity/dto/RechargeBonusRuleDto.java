package com.example.payment.entity.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 充值优惠规则DTO
 * 用于接收前端请求数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class RechargeBonusRuleDto {

    /**
     * 最小金额（分为单位）
     */
    @NotNull(message = "最小金额不能为空")
    @Positive(message = "最小金额必须大于0")
    private Long minAmount;

    /**
     * 最大金额（分为单位），NULL表示无上限
     */
    private Long maxAmount;

    /**
     * 奖励类型：percentage-百分比，fixed-固定金额
     */
    @NotBlank(message = "奖励类型不能为空")
    private String bonusType;

    /**
     * 奖励值：百分比或固定金额
     */
    @NotNull(message = "奖励值不能为空")
    @PositiveOrZero(message = "奖励值不能为负数")
    private BigDecimal bonusValue;

    /**
     * 开始时间，NULL表示无开始时间限制
     */
    private LocalDateTime startTime;

    /**
     * 结束时间，NULL表示无结束时间限制
     */
    private LocalDateTime endTime;

    /**
     * 状态：0-禁用，1-启用
     */
    @NotNull(message = "状态不能为空")
    private Integer status;
}
