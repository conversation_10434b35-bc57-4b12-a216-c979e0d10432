package com.example.payment.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.example.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 快速充值选项实体类
 * 对应数据库表：quick_recharge_options
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("quick_recharge_options")
public class QuickRechargeOption extends BaseEntity {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 充值金额
     */
    private BigDecimal amount;

    /**
     * 赠送金额
     */
    private BigDecimal bonus;

    /**
     * 是否推荐：0-否，1-是
     */
    private Integer isRecommended;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 排序顺序
     */
    private Integer sortOrder;
}
