package com.example.payment.entity.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * VIP套餐DTO
 * 用于接收前端请求数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class VipPlanDto {

    /**
     * 套餐ID（更新时需要）
     */
    private String id;

    /**
     * 套餐名称
     */
    @NotBlank(message = "套餐名称不能为空")
    private String name;

    /**
     * 月付价格（分为单位）
     */
    @NotNull(message = "月付价格不能为空")
    @PositiveOrZero(message = "月付价格不能为负数")
    private Long monthlyPrice;

    /**
     * 年付价格（分为单位）
     */
    @NotNull(message = "年付价格不能为空")
    @PositiveOrZero(message = "年付价格不能为负数")
    private Long yearlyPrice;

    /**
     * 每日消息限制，-1表示无限制
     */
    private Integer dailyMessageLimit;

    /**
     * 历史消息保存天数，-1表示永久保存
     */
    private Integer historyDays;

    /**
     * 套餐描述
     */
    private String description;

    /**
     * 状态：0-下架，1-正常
     */
    private Integer status;
}
