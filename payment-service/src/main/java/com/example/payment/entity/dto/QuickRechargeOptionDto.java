package com.example.payment.entity.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 快速充值选项DTO
 * 用于接收前端请求数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class QuickRechargeOptionDto {

    /**
     * 充值金额（分为单位）
     */
    @NotNull(message = "充值金额不能为空")
    @Positive(message = "充值金额必须大于0")
    private Long amount;

    /**
     * 赠送金额（分为单位）
     */
    @NotNull(message = "赠送金额不能为空")
    @PositiveOrZero(message = "赠送金额不能为负数")
    private Long bonus;

    /**
     * 是否推荐：0-否，1-是
     */
    @NotNull(message = "是否推荐不能为空")
    private Integer isRecommended;

    /**
     * 状态：0-禁用，1-启用
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 排序顺序
     */
    @NotNull(message = "排序顺序不能为空")
    private Integer sortOrder;
}
