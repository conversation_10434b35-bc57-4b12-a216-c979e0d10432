package com.example.payment.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * VIP套餐特性实体类
 * 对应数据库表：vip_plan_features
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@TableName("vip_plan_features")
public class VipPlanFeature {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 套餐ID
     */
    private String planId;

    /**
     * 特性描述
     */
    private String feature;

    /**
     * 排序顺序
     */
    private Integer sortOrder;
}
