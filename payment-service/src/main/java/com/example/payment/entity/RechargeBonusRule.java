package com.example.payment.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.example.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 充值优惠规则实体类
 * 对应数据库表：recharge_bonus_rules
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("recharge_bonus_rules")
public class RechargeBonusRule extends BaseEntity {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 最小金额（分为单位）
     */
    private Long minAmount;

    /**
     * 最大金额（分为单位），NULL表示无上限
     */
    private Long maxAmount;

    /**
     * 奖励类型：percentage-百分比，fixed-固定金额
     */
    private String bonusType;

    /**
     * 奖励值：百分比或固定金额
     */
    private BigDecimal bonusValue;

    /**
     * 开始时间，NULL表示无开始时间限制
     */
    private LocalDateTime startTime;

    /**
     * 结束时间，NULL表示无结束时间限制
     */
    private LocalDateTime endTime;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
}
