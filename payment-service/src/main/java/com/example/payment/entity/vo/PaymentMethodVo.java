package com.example.payment.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 支付方式VO
 * 用于返回给前端的数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class PaymentMethodVo {

    /**
     * 支付方式ID
     */
    private String id;

    /**
     * 支付方式名称
     */
    private String name;

    /**
     * 支付方式描述
     */
    private String description;

    /**
     * 图标
     */
    private String icon;

    private String image;

    /**
     * 是否推荐：0-否，1-是
     */
    private Boolean isRecommended;
    /**
     * 是否推荐文本
     */
    private String isRecommendedText;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 状态文本
     */
    private String statusText;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 是否可用
     */
    private Boolean isAvailable;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
