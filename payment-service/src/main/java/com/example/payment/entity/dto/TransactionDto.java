package com.example.payment.entity.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 交易记录DTO
 * 用于接收前端请求数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class TransactionDto {

    /**
     * 交易ID（更新时需要）
     */
    private String id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 关联订单ID
     */
    private String orderId;

    /**
     * 交易类型：recharge-充值，consume-消费，vip-购买VIP，refund-退款
     */
    @NotBlank(message = "交易类型不能为空")
    private String type;

    /**
     * 交易金额（分为单位）
     */
    @NotNull(message = "交易金额不能为空")
    private Long amount;

    /**
     * 交易后余额（分为单位）
     */
    @NotNull(message = "交易后余额不能为空")
    private Long balance;

    /**
     * 交易描述
     */
    private String description;

    /**
     * 支付方式ID
     */
    private String paymentMethodId;

    /**
     * 交易状态：success-成功，pending-处理中，failed-失败
     */
    private String status;
}
