package com.example.payment.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 用户VIP信息VO
 * 用于返回给前端的数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class UserVipVo {

    /**
     * ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * VIP等级：free-免费用户，vip-VIP会员，super_vip-超级VIP
     */
    private String vipLevel;

    /**
     * VIP等级名称
     */
    private String vipLevelName;

    /**
     * VIP到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireTime;

    /**
     * 账户余额（分为单位）
     */
    private Long balance;

    /**
     * 账户余额（元，格式化显示）
     */
    private String balanceFormatted;

    /**
     * 累计消费金额（分为单位）
     */
    private Long totalSpent;

    /**
     * 累计消费金额（元，格式化显示）
     */
    private String totalSpentFormatted;

    /**
     * 是否已过期
     */
    private Boolean isExpired;

    /**
     * 剩余天数
     */
    private Long remainingDays;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
