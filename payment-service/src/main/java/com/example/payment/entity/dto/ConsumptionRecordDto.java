package com.example.payment.entity.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 消费记录DTO
 * 用于接收前端请求数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class ConsumptionRecordDto {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 关联交易ID
     */
    @NotBlank(message = "关联交易ID不能为空")
    private String transactionId;

    /**
     * 消费类型：model_usage-模型使用，vip_purchase-购买VIP，other-其他
     */
    @NotBlank(message = "消费类型不能为空")
    private String consumptionType;

    /**
     * 模型ID（如果是模型使用）
     */
    private String modelId;

    /**
     * 消费金额（分为单位）
     */
    @NotNull(message = "消费金额不能为空")
    @Positive(message = "消费金额必须大于0")
    private Long amount;
}
