package com.example.payment.entity.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 用户模型使用统计DTO
 * 用于接收前端请求数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class UserModelUsageDto {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 日期
     */
    @NotNull(message = "日期不能为空")
    private LocalDate date;

    /**
     * 模型ID
     */
    @NotBlank(message = "模型ID不能为空")
    private String modelId;

    /**
     * 使用次数
     */
    @NotNull(message = "使用次数不能为空")
    @PositiveOrZero(message = "使用次数不能为负数")
    private Integer usageCount;
}
