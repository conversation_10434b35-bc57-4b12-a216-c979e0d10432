package com.example.payment.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.example.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 用户VIP信息实体类
 * 对应数据库表：user_vip
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("user_vip")
public class UserVip extends BaseEntity {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * VIP等级：free-免费用户，vip-VIP会员，super_vip-超级VIP
     */
    private String vipLevel;

    /**
     * VIP到期时间
     */
    private LocalDateTime expireTime;

    /**
     * 账户余额（分为单位）
     */
    private Long balance;

    /**
     * 累计消费金额（分为单位）
     */
    private Long totalSpent;
}
