package com.example.payment.entity.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 支付方式DTO
 * 用于接收前端请求数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class PaymentMethodDto {

    /**
     * 支付方式ID（更新时需要）
     */
    private String id;

    /**
     * 支付方式名称
     */
    @NotBlank(message = "支付方式名称不能为空")
    private String name;

    /**
     * 支付方式描述
     */
    private String description;

    /**
     * 图标
     */
    private String icon;

    /**
     * 是否推荐：0-否，1-是
     */
    @NotNull(message = "是否推荐不能为空")
    private Integer isRecommended;

    /**
     * 状态：0-禁用，1-启用
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 排序顺序
     */
    @NotNull(message = "排序顺序不能为空")
    private Integer sortOrder;
}
