package com.example.payment.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * VIP订单详情VO
 * 用于返回给前端的数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class VipOrderDetailVo {

    /**
     * ID
     */
    private Long id;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 套餐ID
     */
    private String planId;

    /**
     * 套餐名称
     */
    private String planName;

    /**
     * 购买类型：monthly-月付，yearly-年付
     */
    private String durationType;

    /**
     * 购买类型文本
     */
    private String durationTypeText;

    /**
     * 购买天数
     */
    private Integer durationDays;

    /**
     * 购买天数文本
     */
    private String durationDaysText;

    /**
     * 套餐详情
     */
    private VipPlanVo planDetail;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
