package com.example.payment.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 订单VO
 * 用于返回给前端的数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class OrderVo {

    /**
     * 订单ID
     */
    private String id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 订单类型：vip-购买VIP，recharge-充值
     */
    private String orderType;

    /**
     * 订单类型文本
     */
    private String orderTypeText;

    /**
     * 订单金额（分为单位）
     */
    private Long amount;

    /**
     * 订单金额（元，格式化显示）
     */
    private String amountFormatted;

    /**
     * 支付方式ID
     */
    private String paymentMethodId;

    /**
     * 支付方式名称
     */
    private String paymentMethodName;

    /**
     * 订单状态：pending-待支付，paid-已支付，cancelled-已取消，failed-支付失败
     */
    private String status;

    /**
     * 订单状态文本
     */
    private String statusText;

    /**
     * 订单状态颜色
     */
    private String statusColor;

    /**
     * 是否可以支付
     */
    private Boolean canPay;

    /**
     * 是否可以取消
     */
    private Boolean canCancel;

    /**
     * VIP订单详情（如果是VIP订单）
     */
    private VipOrderDetailVo vipOrderDetail;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;

    /**
     * 取消时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cancelTime;

    /**
     * 订单过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireTime;
}
