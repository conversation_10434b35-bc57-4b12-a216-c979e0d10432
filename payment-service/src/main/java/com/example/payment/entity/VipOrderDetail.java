package com.example.payment.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * VIP订单详情实体类
 * 对应数据库表：vip_order_details
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@TableName("vip_order_details")
public class VipOrderDetail {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 套餐ID
     */
    private String planId;

    /**
     * 购买类型：monthly-月付，yearly-年付
     */
    private String durationType;

    /**
     * 购买天数
     */
    private Integer durationDays;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
