package com.example.payment.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.example.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 用户模型使用统计实体类
 * 对应数据库表：user_model_usage
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("user_model_usage")
public class UserModelUsage extends BaseEntity {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 日期
     */
    private LocalDate date;

    /**
     * 模型ID
     */
    private String modelId;

    /**
     * 使用次数
     */
    private Integer usageCount;
}
