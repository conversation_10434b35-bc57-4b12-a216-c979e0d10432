package com.example.payment.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 快速充值选项VO
 * 用于返回给前端的数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class QuickRechargeOptionVo {

    /**
     * ID
     */
    private Long id;

    /**
     * 充值金额
     */
    private BigDecimal amount;

    private BigDecimal value;

    /**
     * 充值金额（元，格式化显示）
     */
    private String amountFormatted;

    /**
     * 赠送金额
     */
    private BigDecimal bonus;

    /**
     * 赠送金额（元，格式化显示）
     */
    private String bonusFormatted;

    /**
     * 实际到账金额（分为单位）
     */
    private Long totalAmount;

    /**
     * 实际到账金额（元，格式化显示）
     */
    private String totalAmountFormatted;

    /**
     * 优惠百分比
     */
    private Double discountPercentage;

    /**
     * 优惠文本
     */
    private String discountText;

    /**
     * 是否推荐：0-否，1-是
     */
    private Integer isRecommended;

    /**
     * 是否推荐文本
     */
    private String isRecommendedText;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 状态文本
     */
    private String statusText;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 是否可用
     */
    private Boolean isAvailable;

    /**
     * 推荐标签
     */
    private String recommendTag;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
