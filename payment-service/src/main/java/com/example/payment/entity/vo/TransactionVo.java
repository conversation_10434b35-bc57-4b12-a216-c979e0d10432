package com.example.payment.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 交易记录VO
 * 用于返回给前端的数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class TransactionVo {

    /**
     * 交易ID
     */
    private String id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 关联订单ID
     */
    private String orderId;

    /**
     * 交易类型：recharge-充值，consume-消费，vip-购买VIP，refund-退款
     */
    private String type;

    /**
     * 交易类型文本
     */
    private String typeText;

    /**
     * 交易金额（分为单位）
     */
    private Long amount;

    /**
     * 交易金额（元，格式化显示）
     */
    private String amountFormatted;

    /**
     * 交易金额符号（+ 或 -）
     */
    private String amountSign;

    /**
     * 交易后余额（分为单位）
     */
    private Long balance;

    /**
     * 交易后余额（元，格式化显示）
     */
    private String balanceFormatted;

    /**
     * 交易描述
     */
    private String description;

    /**
     * 支付方式ID
     */
    private String paymentMethodId;

    /**
     * 支付方式名称
     */
    private String paymentMethodName;

    /**
     * 交易状态：success-成功，pending-处理中，failed-失败
     */
    private String status;

    /**
     * 交易状态文本
     */
    private String statusText;

    /**
     * 交易状态颜色
     */
    private String statusColor;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
