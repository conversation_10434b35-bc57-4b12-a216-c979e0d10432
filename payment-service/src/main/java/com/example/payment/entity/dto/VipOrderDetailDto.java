package com.example.payment.entity.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * VIP订单详情DTO
 * 用于接收前端请求数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class VipOrderDetailDto {

    /**
     * 订单ID
     */
    @NotBlank(message = "订单ID不能为空")
    private String orderId;

    /**
     * 套餐ID
     */
    @NotBlank(message = "套餐ID不能为空")
    private String planId;

    /**
     * 购买类型：monthly-月付，yearly-年付
     */
    @NotBlank(message = "购买类型不能为空")
    private String durationType;

    /**
     * 购买天数
     */
    @NotNull(message = "购买天数不能为空")
    @Positive(message = "购买天数必须大于0")
    private Integer durationDays;
}
