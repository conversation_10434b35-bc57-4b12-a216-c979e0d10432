package com.example.payment.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 充值优惠规则VO
 * 用于返回给前端的数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class RechargeBonusRuleVo {

    /**
     * ID
     */
    private Long id;

    /**
     * 最小金额（分为单位）
     */
    private Long minAmount;

    /**
     * 最小金额（元，格式化显示）
     */
    private String minAmountFormatted;

    /**
     * 最大金额（分为单位），NULL表示无上限
     */
    private Long maxAmount;

    /**
     * 最大金额（元，格式化显示）
     */
    private String maxAmountFormatted;

    /**
     * 金额范围文本
     */
    private String amountRangeText;

    /**
     * 奖励类型：percentage-百分比，fixed-固定金额
     */
    private String bonusType;

    /**
     * 奖励类型文本
     */
    private String bonusTypeText;

    /**
     * 奖励值：百分比或固定金额
     */
    private BigDecimal bonusValue;

    /**
     * 奖励值显示文本
     */
    private String bonusValueText;

    /**
     * 开始时间，NULL表示无开始时间限制
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间，NULL表示无结束时间限制
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 有效期文本
     */
    private String validPeriodText;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 状态文本
     */
    private String statusText;

    /**
     * 是否当前有效
     */
    private Boolean isCurrentlyValid;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
