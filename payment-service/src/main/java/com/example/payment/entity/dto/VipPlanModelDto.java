package com.example.payment.entity.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * VIP套餐可用模型DTO
 * 用于接收前端请求数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class VipPlanModelDto {

    /**
     * 套餐ID
     */
    @NotBlank(message = "套餐ID不能为空")
    private String planId;

    /**
     * 模型ID
     */
    @NotBlank(message = "模型ID不能为空")
    private String modelId;
}
