package com.example.payment.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.config.annotation.ContentNegotiationConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 * 配置内容协商策略，解决HttpMediaTypeNotAcceptableException异常
 * 
 * <AUTHOR> Service
 * @date 2025-08-01
 */
@Slf4j
@Configuration
public class WebConfig implements WebMvcConfigurer {

    /**
     * 配置内容协商
     * 解决HttpMediaTypeNotAcceptableException异常
     */
    @Override
    public void configureContentNegotiation(ContentNegotiationConfigurer configurer) {
        configurer
                // 启用路径扩展名策略（如 .json, .xml）
                .favorPathExtension(false)
                // 启用参数策略（如 ?format=json）
                .favorParameter(true)
                .parameterName("format")
                // 忽略Accept头信息
                .ignoreAcceptHeader(false)
                // 设置默认内容类型
                .defaultContentType(MediaType.APPLICATION_JSON)
                // 配置媒体类型映射
                .mediaType("json", MediaType.APPLICATION_JSON)
                .mediaType("xml", MediaType.APPLICATION_XML)
                .mediaType("html", MediaType.TEXT_HTML)
                .mediaType("text", MediaType.TEXT_PLAIN);
        
        log.info("Payment Service - 配置内容协商策略完成");
    }
}
