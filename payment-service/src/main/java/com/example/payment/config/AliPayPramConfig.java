package com.example.payment.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Create by 2025/7/29 14:39
 * desc 支付宝配置类
 */
@Slf4j
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Component  // 添加 Spring 组件注解，使其能被 Spring 容器管理
public class AliPayPramConfig {

    @Value("${alipay.appId}")
    private String appId;

    @Value("${alipay.privateKey}")
    private String privateKey;

    @Value("${alipay.alipayPublicKey}")
    private String alipayPublicKey;

    @Value("${alipay.url}")
    private String url;

    @Value("${alipay.notifyUrl}")
    private String notifyUrl;

    @Value("${alipay.synchronousNotifyUrl}")
    private String synchronousNotifyUrl;

}
