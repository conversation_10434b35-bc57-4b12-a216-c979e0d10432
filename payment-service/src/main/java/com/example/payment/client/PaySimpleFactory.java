package com.example.payment.client;

import com.example.common.exception.BusinessException;
import com.example.common.exception.PaymentException;
import com.example.payment.factory.impl.AliPayImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Create by 2025/7/31 15:32
 * desc 支付简单工厂模式
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class PaySimpleFactory {
    private final AliPayImpl aliPay;

    /**
     * 根据支付类型执行支付
     *
     * @param payType 支付类型
     * @return 支付结果
     * @throws PaymentException 当支付类型不支持或支付失败时抛出支付异常
     */
    public String pay(String payType) {
        log.info("开始处理支付请求，支付类型: {}", payType);

        if("aliPay".equals(payType)){
            try {
                String result = new PayClient(aliPay).pay();
                log.info("支付宝支付处理成功");
                return result;
            } catch (Exception e) {
                log.error("支付宝支付处理失败: {}", e.getMessage(), e);
                throw PaymentException.paymentFailed("支付宝", e.getMessage());
            }
        }

        // 记录不支持的支付类型
        log.warn("不支持的支付类型: {}", payType);

        // 抛出支付异常而不是返回错误字符串
        throw PaymentException.unsupportedPaymentType(payType);
    }
}
