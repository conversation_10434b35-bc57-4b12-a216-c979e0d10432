package com.example.payment;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 * Create by 2025/7/22 16:41
 * desc 支付启动类
 */
@SpringBootApplication(scanBasePackages = {"com.example.payment", "com.example.common"})
@MapperScan("com.example.payment.mapper")
@EnableDiscoveryClient
@EnableFeignClients
public class PaymentApplication {
    public static void main(String[] args) {
        //解决bootstrap.yml 文件找不到问题 boot版本高于2.4
        System.setProperty("spring.cloud.bootstrap.enabled", "true");
        SpringApplication.run(PaymentApplication.class, args);
        System.err.println("payment 支付服务启动成功");

    }
}
