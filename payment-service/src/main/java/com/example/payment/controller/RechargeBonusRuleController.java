package com.example.payment.controller;

import com.example.payment.service.RechargeBonusRuleService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 充值优惠规则控制器
 */
@RestController
@RequestMapping("/recharge-bonus-rule")
@RequiredArgsConstructor
public class RechargeBonusRuleController {

    private final RechargeBonusRuleService rechargeBonusRuleService;

}
