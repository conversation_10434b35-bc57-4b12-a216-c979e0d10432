package com.example.payment.controller;

import com.example.common.result.Result;
import com.example.payment.entity.vo.VipPlanVo;
import com.example.payment.service.VipPlanService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * VIP套餐控制器
 */
@RestController
@RequestMapping("/vip-plan")
@RequiredArgsConstructor
public class VipPlanController {

    private final VipPlanService vipPlanService;

    /**
     * 获取所有vip套餐以及套餐特性
     * @return
     */
    @GetMapping("/getAllVipPlansFeatures")
    public Result<List<VipPlanVo>> getAllVipPlansFeatures() {
        return Result.success(vipPlanService.getAllVipPlansFeatures());
    }

}
