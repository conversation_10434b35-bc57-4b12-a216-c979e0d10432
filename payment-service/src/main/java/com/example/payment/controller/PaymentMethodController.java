package com.example.payment.controller;

import com.example.common.result.Result;
import com.example.payment.entity.vo.PaymentMethodVo;
import com.example.payment.service.PaymentMethodService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 支付方式控制器
 */
@RestController
@RequestMapping("/payment-method")
@RequiredArgsConstructor
public class PaymentMethodController {

    private final PaymentMethodService paymentMethodService;

    /**
     * 获取支付方式选项列表
     * @return
     */
    @GetMapping("/paymentMethodList")
    public Result<List<PaymentMethodVo>> list() {
        return Result.success(paymentMethodService.paymentMethodList());
    }

}
