package com.example.payment.controller;

import com.example.common.result.Result;
import com.example.payment.service.QuickRechargeOptionService;
import com.example.payment.service.RechargeService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * Create by 2025/7/28 16:11
 * desc 客户端账号充值相关
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/recharge")
public class RechargeController {
    private final RechargeService rechargeService;

    /**
     * 充值相关异步查询
     * @return
     */
    @GetMapping("/getList")
    public Result getList(){
        Map<String,Object> map = rechargeService.getList();
        return Result.success(map);
    }
}
