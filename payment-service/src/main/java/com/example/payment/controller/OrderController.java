package com.example.payment.controller;

import com.example.common.result.Result;
import com.example.payment.client.PayClient;
import com.example.payment.client.PaySimpleFactory;
import com.example.payment.entity.dto.OrderDto;
import com.example.payment.service.OrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 订单控制器
 */
@RestController
@RequestMapping("/order")
@RequiredArgsConstructor
public class OrderController {

    private final OrderService orderService;

    private final PaySimpleFactory paySimpleFactory;

    //可重入锁
    private final ReentrantLock lock = new ReentrantLock();




    /**
     * 获取唯一订单号
     * @return
     */
    @GetMapping("/getOrderNum")
    public Result getOrderNum() {
        String orderNum = orderService.getOrderNum("2");
        return  Result.success(orderNum);
    }


    /**
     * 根据支付类型 获取预支付下单页面 以及唯一订单号
     * @param payType
     * @return
     */
    @PostMapping("/getPagePayCode")
    public Result getPagePayCode(String payType) {
        ConcurrentHashMap<String, Object> concurrentHashMap = orderService.getPagePayCode(payType);
        return Result.success(concurrentHashMap);
    }


    /**
     * 创建订单
     *
     * @param orderDto
     * @return
     */
    @PostMapping("/createOrder")
    public Result createOrder(@RequestBody @Validated OrderDto orderDto) {
        try {
            if (lock.tryLock(5, TimeUnit.SECONDS)) {
                // 创建订单逻辑
                orderService.createOrder(orderDto);
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            lock.unlock();
        }
        return Result.success();
    }
}
