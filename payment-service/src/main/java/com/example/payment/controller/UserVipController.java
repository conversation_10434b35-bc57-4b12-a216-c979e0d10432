package com.example.payment.controller;

import com.example.common.result.Result;
import com.example.payment.service.UserVipService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户VIP信息控制器
 */
@RestController
@RequestMapping("/user-vip")
@RequiredArgsConstructor
public class UserVipController {

    private final UserVipService userVipService;

    /**
     * 获取用户vip信息
     * @param userId
     * @return
     */
    @GetMapping("/getUserViPInfo")
    public Result getUserViPInfo(Long userId) {
        return Result.success(userVipService.getUserVipInfo(userId));
    }

}
