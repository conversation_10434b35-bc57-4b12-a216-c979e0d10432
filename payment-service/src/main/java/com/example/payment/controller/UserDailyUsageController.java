package com.example.payment.controller;

import com.example.payment.service.UserDailyUsageService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户每日使用统计控制器
 */
@RestController
@RequestMapping("/user-daily-usage")
@RequiredArgsConstructor
public class UserDailyUsageController {

    private final UserDailyUsageService userDailyUsageService;

}
