package com.example.payment.controller;

import com.example.payment.service.VipPlanFeatureService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * VIP套餐特性控制器
 */
@RestController
@RequestMapping("/vip-plan-feature")
@RequiredArgsConstructor
public class VipPlanFeatureController {

    private final VipPlanFeatureService vipPlanFeatureService;

}
