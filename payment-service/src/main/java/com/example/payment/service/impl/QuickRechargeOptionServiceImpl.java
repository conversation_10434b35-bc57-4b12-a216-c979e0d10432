package com.example.payment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.payment.entity.QuickRechargeOption;
import com.example.payment.entity.vo.QuickRechargeOptionVo;
import com.example.payment.mapper.QuickRechargeOptionMapper;
import com.example.payment.service.QuickRechargeOptionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * 快速充值选项服务实现类
 */
@Service
@Slf4j
public class QuickRechargeOptionServiceImpl extends ServiceImpl<QuickRechargeOptionMapper, QuickRechargeOption> implements QuickRechargeOptionService {
    @Resource
    private QuickRechargeOptionMapper quickRechargeOptionMapper;

    @Override
    public List<QuickRechargeOptionVo> getList() {
        log.info("获取充值列表");
        return quickRechargeOptionMapper.getList();
    }
}
