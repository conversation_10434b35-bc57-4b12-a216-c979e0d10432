package com.example.payment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.common.utils.GenericRedisUtil;
import com.example.payment.entity.VipPlan;
import com.example.payment.entity.vo.VipPlanVo;
import com.example.payment.enums.VipDescEnum;
import com.example.payment.mapper.VipPlanMapper;
import com.example.payment.service.VipPlanService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * VIP套餐服务实现类
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class VipPlanServiceImpl extends ServiceImpl<VipPlanMapper, VipPlan> implements VipPlanService {

    private final VipPlanMapper vipPlanMapper;

    private final GenericRedisUtil genericRedisUtil;

    @Override
    public List<VipPlanVo> getAllVipPlansFeatures() {
        //TODO 目前没有管理端暂时从数据库直接获取 从缓存获取有数据一致性问题
//        //从缓存中获取vip套餐以及特性
//        List<VipPlanVo> vipPlanVos = genericRedisUtil.listGetAll(VipDescEnum.VIP_PLAN.getDesc(), VipPlanVo.class);
        List<VipPlanVo> vipPlanVos = vipPlanMapper.getAllVipPlansFeatures();
        return vipPlanVos;
    }
}
