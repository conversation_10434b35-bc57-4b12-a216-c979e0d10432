package com.example.payment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.payment.entity.PaymentMethod;
import com.example.payment.entity.vo.PaymentMethodVo;

import java.util.List;

/**
 * 支付方式服务接口
 */
public interface PaymentMethodService extends IService<PaymentMethod> {
    /**
     * 获取支付方式选项列表
     * @return
     */
    List<PaymentMethodVo> paymentMethodList();
}
