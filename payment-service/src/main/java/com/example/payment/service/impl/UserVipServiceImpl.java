package com.example.payment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.payment.entity.UserVip;
import com.example.payment.entity.vo.UserVipVo;
import com.example.payment.mapper.UserVipMapper;
import com.example.payment.service.UserVipService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 用户VIP信息服务实现类
 */
@Service
public class UserVipServiceImpl extends ServiceImpl<UserVipMapper, UserVip> implements UserVipService {
    @Resource
    private UserVipMapper userVipMapper;

    @Override
    public UserVipVo getUserVipInfo(Long userId) {
        return userVipMapper.getUserVipInfo(userId);
    }
}
