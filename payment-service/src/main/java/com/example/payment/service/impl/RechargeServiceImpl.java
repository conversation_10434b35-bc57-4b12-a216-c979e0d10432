package com.example.payment.service.impl;

import com.example.payment.entity.vo.PaymentMethodVo;
import com.example.payment.entity.vo.QuickRechargeOptionVo;
import com.example.payment.future.RechargeCompletableFuture;
import com.example.payment.service.RechargeService;
import jakarta.annotation.Resource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * Create by 2025/7/28 16:14
 * desc
 */
@Service
public class RechargeServiceImpl implements RechargeService {

    @Resource
    private RechargeCompletableFuture rechargeCompletableFuture;

    @Override
    public Map<String, Object> getList() {
        HashMap<String, Object> rechargeMap = new HashMap<>();
        try {
            //异步获取充值列表
            CompletableFuture<List<QuickRechargeOptionVo>> listCompletableFuture = rechargeCompletableFuture.queryRechargeList();
            List<QuickRechargeOptionVo> quickRechargeOptionVoList = listCompletableFuture.get();

            //支付方式列表
            CompletableFuture<List<PaymentMethodVo>> CompletableFuturePay = rechargeCompletableFuture.queryPaymentMethodList();
            List<PaymentMethodVo> paymentMethodVoList = CompletableFuturePay.get();

            rechargeMap.put("quickRechargeOptionVoList", quickRechargeOptionVoList);
            rechargeMap.put("paymentMethodVoList", paymentMethodVoList);



            return  rechargeMap;
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
    }
}
