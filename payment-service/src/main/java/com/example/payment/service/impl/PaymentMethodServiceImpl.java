package com.example.payment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.payment.entity.PaymentMethod;
import com.example.payment.entity.vo.PaymentMethodVo;
import com.example.payment.mapper.PaymentMethodMapper;
import com.example.payment.service.PaymentMethodService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 支付方式服务实现类
 */
@Service
@RequiredArgsConstructor
public class PaymentMethodServiceImpl extends ServiceImpl<PaymentMethodMapper, PaymentMethod> implements PaymentMethodService {

    private final PaymentMethodMapper paymentMethodMapper;

    @Override
    public List<PaymentMethodVo> paymentMethodList() {
        return paymentMethodMapper.paymentMethodList();
    }
}
