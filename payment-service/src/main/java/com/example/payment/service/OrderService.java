package com.example.payment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.payment.entity.Order;
import com.example.payment.entity.dto.OrderDto;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 订单服务接口
 */
public interface OrderService extends IService<Order> {

    /**
     * 创建订单
     * @param orderDto
     */
    void createOrder(OrderDto orderDto);

    /**
     * 获取唯一订单号
     * @return
     */
    String getOrderNum(String sourceType);

    /**
     * 根据支付类型 获取预支付下单页面 以及唯一订单号
     * @param payType
     * @return
     */
    ConcurrentHashMap<String, Object> getPagePayCode(String payType);
}
