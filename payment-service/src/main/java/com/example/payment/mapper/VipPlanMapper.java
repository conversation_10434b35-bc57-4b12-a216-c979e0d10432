package com.example.payment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.payment.entity.VipPlan;
import com.example.payment.entity.vo.VipPlanVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * VIP套餐Mapper接口
 */
@Mapper
public interface VipPlanMapper extends BaseMapper<VipPlan> {

    /**
     * 获取所有vip套餐以及套餐特性
     * @return
     */
    List<VipPlanVo> getAllVipPlansFeatures();
}
