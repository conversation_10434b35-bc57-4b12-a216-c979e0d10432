package com.example.payment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.payment.entity.QuickRechargeOption;
import com.example.payment.entity.vo.QuickRechargeOptionVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 快速充值选项Mapper接口
 */
@Mapper
public interface QuickRechargeOptionMapper extends BaseMapper<QuickRechargeOption> {

    @Select("SELECT *,amount as 'value' FROM quick_recharge_options WHERE status = 1 ORDER BY sort_order")
    List<QuickRechargeOptionVo> getList();

}
