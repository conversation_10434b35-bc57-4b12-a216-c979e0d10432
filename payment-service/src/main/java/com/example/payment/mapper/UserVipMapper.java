package com.example.payment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.payment.entity.UserVip;
import com.example.payment.entity.vo.UserVipVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 用户VIP信息Mapper接口
 */
@Mapper
public interface UserVipMapper extends BaseMapper<UserVip> {

    @Select("SELECT * FROM user_vip WHERE user_id = #{userId} limit 0,1")
    UserVipVo getUserVipInfo(Long userId);

}
