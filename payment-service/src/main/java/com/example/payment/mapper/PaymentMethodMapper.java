package com.example.payment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.payment.entity.PaymentMethod;
import com.example.payment.entity.vo.PaymentMethodVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 支付方式Mapper接口
 */
@Mapper
public interface PaymentMethodMapper extends BaseMapper<PaymentMethod> {

    @Select("SELECT * FROM payment_methods WHERE status = 1 order by sort_order")
    List<PaymentMethodVo> paymentMethodList();
}
