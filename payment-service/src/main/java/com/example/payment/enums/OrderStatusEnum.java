package com.example.payment.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单状态枚举
 */
@Getter
@AllArgsConstructor
public enum OrderStatusEnum {

    /**
     * 待支付
     */
    PENDING("pending", "待支付"),

    /**
     * 已支付
     */
    PAID("paid", "已支付"),

    /**
     * 已取消
     */
    CANCELLED("cancelled", "已取消"),

    /**
     * 支付失败
     */
    FAILED("failed", "支付失败");

    /**
     * 状态代码
     */
    private final String code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据代码获取枚举
     */
    public static OrderStatusEnum getByCode(String code) {
        for (OrderStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
