package com.example.payment.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * VIP等级枚举
 */
@Getter
@AllArgsConstructor
public enum VipLevelEnum {

    /**
     * 免费用户
     */
    FREE("free", "免费用户"),

    /**
     * VIP会员
     */
    VIP("vip", "VIP会员"),

    /**
     * 超级VIP
     */
    SUPER_VIP("super_vip", "超级VIP");

    /**
     * 等级代码
     */
    private final String code;

    /**
     * 等级名称
     */
    private final String name;

    /**
     * 根据代码获取枚举
     */
    public static VipLevelEnum getByCode(String code) {
        for (VipLevelEnum level : values()) {
            if (level.getCode().equals(code)) {
                return level;
            }
        }
        return null;
    }
}
