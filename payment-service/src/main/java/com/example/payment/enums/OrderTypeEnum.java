package com.example.payment.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单类型枚举
 */
@Getter
@AllArgsConstructor
public enum OrderTypeEnum {

    /**
     * 购买VIP
     */
    VIP("vip", "购买VIP"),

    /**
     * 充值
     */
    RECHARGE("recharge", "充值");

    /**
     * 类型代码
     */
    private final String code;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 根据代码获取枚举
     */
    public static OrderTypeEnum getByCode(String code) {
        for (OrderTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
